@echo off
echo 正在搜索系统中的Java安装...
echo.

echo 检查常见的Java安装位置：
echo.

echo 1. 检查 Program Files...
if exist "C:\Program Files\Java" (
    echo ✅ 找到: C:\Program Files\Java
    dir "C:\Program Files\Java" /b
) else (
    echo ❌ 未找到: C:\Program Files\Java
)

echo.
echo 2. 检查 Program Files (x86)...
if exist "C:\Program Files (x86)\Java" (
    echo ✅ 找到: C:\Program Files (x86)\Java
    dir "C:\Program Files (x86)\Java" /b
) else (
    echo ❌ 未找到: C:\Program Files (x86)\Java
)

echo.
echo 3. 检查其他可能位置...
for %%d in (C D E F) do (
    if exist "%%d:\jdk" (
        echo ✅ 找到: %%d:\jdk
        dir "%%d:\jdk" /b
    )
    if exist "%%d:\java" (
        echo ✅ 找到: %%d:\java
        dir "%%d:\java" /b
    )
)

echo.
echo 4. 当前环境变量：
echo JAVA_HOME = %JAVA_HOME%
echo PATH = %PATH%

echo.
echo 搜索完成
pause
