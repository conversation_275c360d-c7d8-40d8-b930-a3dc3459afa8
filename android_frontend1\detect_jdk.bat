@echo off
chcp 65001 >nul
echo.
echo ===========================================
echo          JDK Auto Detection Tool
echo ===========================================
echo.

set "SDK_PATH=E:\SDK"
set "JDK_FOUND="
set "BEST_JDK="

echo [1/4] Checking Android SDK JDK directory...
if exist "%SDK_PATH%\jdks" (
    echo Found SDK jdks directory: %SDK_PATH%\jdks
    echo.
    echo Available JDK versions:
    for /d %%i in ("%SDK_PATH%\jdks\*") do (
        if exist "%%i\bin\java.exe" (
            echo   - %%~ni
            set "JDK_FOUND=1"
            set "BEST_JDK=%%i"
        )
    )
) else (
    echo SDK jdks directory not found: %SDK_PATH%\jdks
)

echo.
echo [2/4] Checking common JDK locations...
set "COMMON_PATHS=C:\Program Files\Java C:\Program Files (x86)\Java C:\Java"
for %%p in (%COMMON_PATHS%) do (
    if exist "%%p" (
        echo Checking: %%p
        for /d %%j in ("%%p\jdk*") do (
            if exist "%%j\bin\java.exe" (
                echo   Found: %%~nj
                set "JDK_FOUND=1"
                if not defined BEST_JDK set "BEST_JDK=%%j"
            )
        )
    )
)

echo.
echo [3/4] Checking current JAVA_HOME...
if defined JAVA_HOME (
    if exist "%JAVA_HOME%\bin\java.exe" (
        echo Current JAVA_HOME is valid: %JAVA_HOME%
        java -version 2>&1 | findstr "version"
    ) else (
        echo Current JAVA_HOME is invalid: %JAVA_HOME%
    )
) else (
    echo JAVA_HOME is not set
)

echo.
echo [4/4] Detection Results:
echo ==========================================
if defined JDK_FOUND (
    echo ✅ JDK found successfully!
    if defined BEST_JDK (
        echo.
        echo Recommended JDK: %BEST_JDK%
        echo.
        echo Testing JDK version...
        "%BEST_JDK%\bin\java.exe" -version 2>&1
        echo.
        
        set /p "auto_set=Do you want to set this as JAVA_HOME? (y/n): "
        if /i "!auto_set!"=="y" (
            echo.
            echo Setting JAVA_HOME to: %BEST_JDK%
            set "JAVA_HOME=%BEST_JDK%"
            setx JAVA_HOME "%BEST_JDK%" >nul
            echo ✅ JAVA_HOME has been set successfully!
            echo.
            echo You can now run build commands:
            echo   build_final.bat
            echo   build_clean.bat
        )
    )
) else (
    echo ❌ No JDK found in common locations
    echo.
    echo Please install JDK 11 or 17:
    echo 1. Download from: https://adoptium.net/
    echo 2. Or use Android Studio's embedded JDK
    echo 3. Manual setup: Run fix_java.bat for manual configuration
)

echo.
echo ==========================================
echo Detection complete. Press any key to exit...
pause >nul
