@echo off
echo Cleaning Gradle cache and temporary files...

:: Delete .gradle directory
if exist ".gradle" (
    echo Deleting .gradle directory...
    rmdir /s /q ".gradle"
)

:: Delete build directories
if exist "build" (
    echo Deleting build directory...
    rmdir /s /q "build"
)

if exist "app\build" (
    echo Deleting app\build directory...
    rmdir /s /q "app\build"
)

:: Delete user gradle cache
if exist "%USERPROFILE%\.gradle\caches" (
    echo Deleting user gradle cache...
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
)

echo.
echo ✅ Gradle cache cleaned!
echo You can now try building again.
pause
