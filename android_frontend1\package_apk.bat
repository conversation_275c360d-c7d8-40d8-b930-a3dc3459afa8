@echo off
chcp 65001 >nul
echo.
echo ██████╗  █████╗ ███╗   ██╗██╗   ██╗██╗██╗  ██╗██╗   ██╗ █████╗ 
echo ██╔══██╗██╔══██╗████╗  ██║╚██╗ ██╔╝██║██║  ██║██║   ██║██╔══██╗
echo ██████╔╝███████║██╔██╗ ██║ ╚████╔╝ ██║███████║██║   ██║███████║
echo ██╔══██╗██╔══██║██║╚██╗██║  ╚██╔╝  ██║██╔══██║██║   ██║██╔══██║
echo ██║  ██║██║  ██║██║ ╚████║   ██║   ██║██║  ██║╚██████╔╝██║  ██║
echo ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚═╝  ╚═╝
echo.
echo 🚀 随意花小贷 - Android APK 专业打包工具
echo 📱 WebView 混合应用 | 后端：Web服务器 | 前端：原生APP
echo ═══════════════════════════════════════════════════════════════
echo.

REM 设置变量
set APP_NAME=随意花
set PACKAGE_NAME=com.dailuanshej.loan
set OUTPUT_DIR=apk_output
set WEBSITE_URL=https://dailuanshej.cn

echo 📋 项目信息：
echo    应用名称: %APP_NAME%
echo    包名: %PACKAGE_NAME%
echo    网站地址: %WEBSITE_URL%
echo    输出目录: %OUTPUT_DIR%
echo.

REM 检查环境
echo 🔍 第一步：环境检查
java -version 2>&1 | findstr "version" >nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境
    echo 请安装 Java 8-19 版本
    pause
    exit /b 1
)
echo ✅ Java 环境正常

echo.
echo 🧹 第二步：清理构建缓存
if exist "app\build" rmdir /s /q "app\build"
if exist ".gradle" rmdir /s /q ".gradle"
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"
mkdir "%OUTPUT_DIR%"
echo ✅ 缓存清理完成

echo.
echo 🔨 第三步：开始构建APK
echo ⏱️  预计时间：3-8分钟（首次构建较慢）
echo.

REM 设置构建环境
set GRADLE_OPTS=-Xmx4096m -Dfile.encoding=UTF-8

echo [1/4] 检查 Gradle 版本...
call gradlew.bat --version --quiet
if %errorlevel% neq 0 (
    echo ❌ Gradle 初始化失败
    pause
    exit /b 1
)

echo [2/4] 清理项目...
call gradlew.bat clean
if %errorlevel% neq 0 (
    echo ❌ 项目清理失败
    pause
    exit /b 1
)

echo [3/4] 构建调试版 APK...
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 调试版构建失败
    echo 💡 请检查代码和配置文件
    pause
    exit /b 1
)

echo [4/4] 构建发布版 APK...
call gradlew.bat assembleRelease
set RELEASE_RESULT=%errorlevel%

echo.
echo 📦 第四步：处理输出文件
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    copy "app\build\outputs\apk\debug\app-debug.apk" "%OUTPUT_DIR%\%APP_NAME%-调试版.apk" >nul
    echo ✅ 调试版APK已生成: %OUTPUT_DIR%\%APP_NAME%-调试版.apk
    
    for %%A in ("%OUTPUT_DIR%\%APP_NAME%-调试版.apk") do (
        set /a debug_size=%%~zA/1024/1024
        echo    文件大小: !debug_size! MB
    )
)

if %RELEASE_RESULT% equ 0 (
    if exist "app\build\outputs\apk\release\app-release-unsigned.apk" (
        copy "app\build\outputs\apk\release\app-release-unsigned.apk" "%OUTPUT_DIR%\%APP_NAME%-发布版-未签名.apk" >nul
        echo ✅ 发布版APK已生成: %OUTPUT_DIR%\%APP_NAME%-发布版-未签名.apk
        
        for %%A in ("%OUTPUT_DIR%\%APP_NAME%-发布版-未签名.apk") do (
            set /a release_size=%%~zA/1024/1024
            echo    文件大小: !release_size! MB
        )
    )
) else (
    echo ⚠️  发布版构建失败，但调试版可用
)

echo.
echo 🎉 APK 打包完成！
echo.
echo 📱 安装说明：
echo 1. 将APK文件传输到Android设备
echo 2. 在设备设置中启用"未知来源"安装
echo 3. 点击APK文件进行安装
echo 4. 首次启动时授予通讯录读取权限
echo.
echo 🔧 技术特性：
echo ✅ WebView混合架构 - 90%%功能在网页端开发
echo ✅ 原生通讯录读取 - JavaScript接口无缝调用
echo ✅ 网络安全配置 - 支持HTTPS和本地测试
echo ✅ 自动权限管理 - 用户友好的权限请求
echo ✅ 响应式设计 - 适配各种屏幕尺寸
echo.
echo 💡 开发提示：
echo - 网页内容修改后，只需重启APP即可看到更新
echo - 无需重新打包APK，开发效率极高
echo - 90%%的功能开发都在Web端进行
echo.

REM 检查是否连接了设备
adb devices 2>nul | findstr "device" >nul
if %errorlevel% equ 0 (
    echo 📲 检测到Android设备，是否立即安装测试？ (y/n)
    set /p install_choice=
    if /i "%install_choice%"=="y" (
        echo 📲 正在安装调试版到设备...
        adb install -r "%OUTPUT_DIR%\%APP_NAME%-调试版.apk"
        if %errorlevel% equ 0 (
            echo ✅ 安装成功！可以在设备上测试了
        ) else (
            echo ❌ 安装失败，请手动安装APK文件
        )
    )
) else (
    echo 💡 未检测到设备，请手动安装APK文件
)

echo.
echo 🔗 相关链接：
echo    网站前端: %WEBSITE_URL%
echo    后台管理: %WEBSITE_URL%/admin.php
echo    通讯录接口: JavaScript bridge 已自动注入
echo.
echo 🎯 打包工具使用完毕
pause
