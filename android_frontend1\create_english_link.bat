@echo off
echo ========================================
echo    Create English Path Symbolic Link
echo ========================================
echo.

echo This script will create an English path link to avoid Chinese path issues.
echo.
echo Target: E:\仿随意花小贷源码完整版\android_frontend
echo Link:   C:\SuiYiHua
echo.

echo NOTE: This requires Administrator privileges
echo.
set /p confirm="Continue? (y/n): "

if /i "%confirm%"=="y" (
    echo.
    echo Creating symbolic link...
    mklink /D "C:\SuiYiHua" "E:\仿随意花小贷源码完整版\android_frontend"
    
    if %ERRORLEVEL% equ 0 (
        echo.
        echo ✅ Symbolic link created successfully!
        echo.
        echo You can now use the English path:
        echo C:\SuiYiHua
        echo.
        echo To build APK, run:
        echo cd C:\SuiYiHua
        echo gradlew.bat assembleDebug
        echo.
    ) else (
        echo.
        echo ❌ Failed to create symbolic link
        echo Please run this script as Administrator
        echo.
    )
) else (
    echo Operation cancelled.
)

pause
