<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APK功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 APK通讯录功能测试</h1>
        
        <div id="status" class="status info">
            等待测试...
        </div>
        
        <div>
            <button class="btn" onclick="checkEnvironment()">🔍 检查环境</button>
            <button class="btn" onclick="checkPermission()">🔐 检查权限</button>
            <button class="btn" onclick="requestPermission()">📋 申请权限</button>
            <button class="btn" onclick="readContacts()">📞 读取通讯录</button>
            <button class="btn" onclick="getDebugInfo()">🐛 调试信息</button>
            <button class="btn" onclick="clearLog()">🧹 清空日志</button>
        </div>
        
        <div id="results">
            <h3>测试结果：</h3>
            <div id="log" class="log">等待测试结果...</div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            
            console.log(logMessage);
            
            if (logElement) {
                logElement.innerHTML += logMessage + '\n';
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        function updateStatus(message, className = 'info') {
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.className = 'status ' + className;
            }
        }
        
        function clearLog() {
            if (logElement) {
                logElement.innerHTML = '';
            }
            updateStatus('日志已清空', 'info');
        }
        
        function checkEnvironment() {
            log('=== 环境检查开始 ===');
            
            // 基本信息
            log('User Agent: ' + navigator.userAgent);
            log('页面URL: ' + location.href);
            log('协议: ' + location.protocol);
            
            // 检查Android接口
            const hasAndroidInterface = typeof window.AndroidInterface !== 'undefined';
            const hasAndroidContacts = typeof window.AndroidContacts !== 'undefined';
            
            log('AndroidInterface: ' + (hasAndroidInterface ? '✅ 可用' : '❌ 不可用'));
            log('AndroidContacts: ' + (hasAndroidContacts ? '✅ 可用' : '❌ 不可用'));
            
            if (hasAndroidInterface) {
                log('=== AndroidInterface 方法检查 ===');
                const methods = [
                    'hasContactsPermission',
                    'requestContactsPermission',
                    'getContacts',
                    'requestContacts',
                    'checkPermission',
                    'showToast',
                    'testPermissionRequest',
                    'getDebugInfo',
                    'forcePermissionCheck'
                ];
                
                methods.forEach(method => {
                    const available = typeof window.AndroidInterface[method] === 'function';
                    log(`  ${method}: ${available ? '✅' : '❌'}`);
                });
                
                updateStatus('环境检查完成 - AndroidInterface可用', 'success');
            } else {
                updateStatus('环境检查完成 - AndroidInterface不可用', 'error');
            }
            
            log('=== 环境检查完成 ===\n');
        }
        
        function checkPermission() {
            log('=== 权限检查开始 ===');
            
            if (!window.AndroidInterface) {
                log('❌ AndroidInterface不可用');
                updateStatus('权限检查失败 - 接口不可用', 'error');
                return;
            }
            
            try {
                if (typeof window.AndroidInterface.hasContactsPermission === 'function') {
                    const hasPermission = window.AndroidInterface.hasContactsPermission();
                    log('权限状态: ' + (hasPermission ? '✅ 已授权' : '❌ 未授权'));
                    updateStatus(`权限状态: ${hasPermission ? '已授权' : '未授权'}`, 
                                hasPermission ? 'success' : 'warning');
                } else {
                    log('❌ hasContactsPermission 方法不可用');
                    updateStatus('权限检查失败 - 方法不可用', 'error');
                }
            } catch (e) {
                log('❌ 权限检查异常: ' + e.message);
                updateStatus('权限检查异常', 'error');
            }
            
            log('=== 权限检查完成 ===\n');
        }
        
        function requestPermission() {
            log('=== 权限申请开始 ===');
            
            if (!window.AndroidInterface) {
                log('❌ AndroidInterface不可用');
                updateStatus('权限申请失败 - 接口不可用', 'error');
                return;
            }
            
            // 设置权限结果回调
            window.onContactsPermissionResult = function(granted) {
                log('📞 权限申请结果回调: ' + (granted ? '✅ 已授权' : '❌ 被拒绝'));
                updateStatus(`权限申请结果: ${granted ? '已授权' : '被拒绝'}`, 
                            granted ? 'success' : 'error');
                
                if (granted) {
                    log('✅ 权限授权成功，可以读取通讯录了');
                } else {
                    log('❌ 权限被拒绝，请在系统设置中手动授权');
                }
            };
            
            try {
                log('发送权限申请请求...');
                updateStatus('正在申请权限...', 'warning');
                window.AndroidInterface.requestContactsPermission();
                log('✅ 权限申请请求已发送，请在弹窗中授权');
            } catch (e) {
                log('❌ 权限申请异常: ' + e.message);
                updateStatus('权限申请异常', 'error');
            }
            
            log('=== 权限申请完成 ===\n');
        }
        
        function readContacts() {
            log('=== 通讯录读取开始 ===');
            
            if (!window.AndroidInterface) {
                log('❌ AndroidInterface不可用');
                updateStatus('通讯录读取失败 - 接口不可用', 'error');
                return;
            }
            
            try {
                // 先检查权限
                if (typeof window.AndroidInterface.hasContactsPermission === 'function') {
                    const hasPermission = window.AndroidInterface.hasContactsPermission();
                    log('当前权限状态: ' + (hasPermission ? '✅ 已授权' : '❌ 未授权'));
                    
                    if (!hasPermission) {
                        log('⚠️ 权限未授权，请先申请权限');
                        updateStatus('需要先申请权限', 'warning');
                        return;
                    }
                }
                
                // 读取通讯录
                if (typeof window.AndroidInterface.getContacts === 'function') {
                    log('开始读取通讯录数据...');
                    updateStatus('正在读取通讯录...', 'warning');
                    
                    const contactsData = window.AndroidInterface.getContacts();
                    log('原始数据长度: ' + contactsData.length);
                    
                    if (contactsData && contactsData !== '[]') {
                        try {
                            const contacts = JSON.parse(contactsData);
                            log('✅ 解析成功，联系人数量: ' + contacts.length);
                            
                            if (contacts.length > 0) {
                                log('=== 前5个联系人 ===');
                                contacts.slice(0, 5).forEach((contact, index) => {
                                    log(`${index + 1}. ${contact.name || '未知'} - ${contact.phone || '无号码'}`);
                                });
                                
                                updateStatus(`读取成功 - ${contacts.length}个联系人`, 'success');
                            } else {
                                log('⚠️ 通讯录为空');
                                updateStatus('通讯录为空', 'warning');
                            }
                        } catch (e) {
                            log('❌ 数据解析失败: ' + e.message);
                            log('原始数据: ' + contactsData.substring(0, 200) + '...');
                            updateStatus('数据解析失败', 'error');
                        }
                    } else {
                        log('⚠️ 未获取到通讯录数据');
                        updateStatus('未获取到数据', 'warning');
                    }
                } else {
                    log('❌ getContacts 方法不可用');
                    updateStatus('读取方法不可用', 'error');
                }
            } catch (e) {
                log('❌ 通讯录读取异常: ' + e.message);
                updateStatus('读取异常', 'error');
            }
            
            log('=== 通讯录读取完成 ===\n');
        }
        
        function getDebugInfo() {
            log('=== 调试信息获取开始 ===');
            
            if (!window.AndroidInterface) {
                log('❌ AndroidInterface不可用');
                updateStatus('调试信息获取失败 - 接口不可用', 'error');
                return;
            }
            
            try {
                if (typeof window.AndroidInterface.getDebugInfo === 'function') {
                    const debugInfo = window.AndroidInterface.getDebugInfo();
                    log('调试信息: ' + debugInfo);
                    
                    try {
                        const info = JSON.parse(debugInfo);
                        log('=== 详细调试信息 ===');
                        Object.keys(info).forEach(key => {
                            log(`${key}: ${info[key]}`);
                        });
                        updateStatus('调试信息获取成功', 'success');
                    } catch (e) {
                        log('调试信息解析失败: ' + e.message);
                        updateStatus('调试信息解析失败', 'error');
                    }
                } else {
                    log('❌ getDebugInfo 方法不可用');
                    updateStatus('调试方法不可用', 'error');
                }
                
                // 强制权限检查
                if (typeof window.AndroidInterface.forcePermissionCheck === 'function') {
                    log('执行强制权限检查...');
                    window.AndroidInterface.forcePermissionCheck();
                }
            } catch (e) {
                log('❌ 调试信息获取异常: ' + e.message);
                updateStatus('调试信息获取异常', 'error');
            }
            
            log('=== 调试信息获取完成 ===\n');
        }
        
        // 页面加载完成后自动检查环境
        window.addEventListener('load', function() {
            log('📱 APK功能测试页面已加载');
            updateStatus('页面已加载，请点击按钮开始测试', 'info');
            
            // 延迟自动检查环境
            setTimeout(function() {
                log('自动执行环境检查...');
                checkEnvironment();
            }, 1000);
        });
        
        // 监听通讯录数据加载事件
        document.addEventListener('contactsLoaded', function(event) {
            log('📞 收到 contactsLoaded 事件，数据量: ' + event.detail.length);
        });
        
        // 全局错误捕获
        window.addEventListener('error', function(event) {
            log('❌ JavaScript错误: ' + event.error.message);
        });
    </script>
</body>
</html>
