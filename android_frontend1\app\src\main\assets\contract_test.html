<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-link {
            display: block;
            padding: 15px;
            background: #007aff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        .test-link:hover {
            background: #0056b3;
        }
        h2 {
            color: #333;
            margin-bottom: 15px;
        }
        .description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>📱 移动端合同页面测试</h2>
        <div class="description">
            测试今天更新的web前端合同功能，所有文字已设置为横排显示。
        </div>
        
        <a href="customer_contract.php?id=1" class="test-link">
            🖥️ 桌面版合同页面 (客户ID: 1)
        </a>
        
        <a href="customer_contract_mobile.php?id=1" class="test-link">
            📱 移动端合同页面 (客户ID: 1)
        </a>
        
        <a href="customer_contract_mobile_h5.php?id=1" class="test-link">
            📱 移动端H5优化版合同页面 (客户ID: 1)
        </a>
        
        <a href="customer_view.php?id=1" class="test-link">
            👤 客户详情页面 (客户ID: 1)
        </a>
    </div>
    
    <div class="test-container">
        <h2>🔧 更新说明</h2>
        <div class="description">
            <strong>今日更新内容 (2025-07-13):</strong><br>
            ✅ 更新了web前端合同相关文件<br>
            ✅ 移动端合同页面文字改为横排显示<br>
            ✅ 保持SDK版本和Gradle版本不变<br>
            ✅ 联系人读取功能完全保持不变<br>
            ✅ 添加了H5优化版本合同页面<br>
            ✅ 统一了数据源管理
        </div>
    </div>
    
    <div class="test-container">
        <h2>📋 测试清单</h2>
        <div class="description">
            <strong>请测试以下功能:</strong><br>
            □ 合同页面文字是否横排显示<br>
            □ 移动端页面是否正常加载<br>
            □ 合同信息是否正确显示<br>
            □ 编辑功能是否正常工作<br>
            □ 数据保存是否成功<br>
            □ 联系人读取功能是否正常
        </div>
    </div>
    
    <script>
        console.log('📱 合同页面测试工具已加载');
        console.log('🔧 今日更新: web前端合同功能，文字横排显示');
    </script>
</body>
</html>
