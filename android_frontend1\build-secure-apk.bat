@echo off
echo ========================================
echo 🔒 构建防报毒APK - 安全优化版本
echo ========================================

echo.
echo 📋 安全优化内容：
echo   • 网络安全配置
echo   • 应用完整性检查
echo   • 代码混淆保护
echo   • 签名验证机制
echo   • 防篡改检测
echo.

echo 🔧 步骤1: 清理旧的构建文件...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 已清理 app\build 目录
)

if exist "build" (
    rmdir /s /q "build"
    echo ✅ 已清理 build 目录
)

echo.
echo 🔒 步骤2: 构建Release版本（启用混淆）...
call gradlew clean
if %errorlevel% neq 0 (
    echo ❌ Clean 失败
    pause
    exit /b 1
)

call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo ❌ Release构建失败
    echo.
    echo 💡 如果遇到签名问题，请：
    echo   1. 确保您的签名文件路径正确
    echo   2. 检查签名密码是否正确
    echo   3. 或者先构建Debug版本测试
    pause
    exit /b 1
)

echo.
echo 📦 步骤3: 复制安全APK文件...
set APK_SOURCE=app\build\outputs\apk\release\app-release.apk
set APK_DEST=youyihua_v1.0.3_secure.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%"
    echo ✅ 安全APK已复制到: %APK_DEST%
) else (
    echo ❌ 找不到Release APK，尝试Debug版本...
    set APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk
    set APK_DEST=youyihua_v1.0.3_secure_debug.apk
    
    if exist "%APK_SOURCE%" (
        copy "%APK_SOURCE%" "%APK_DEST%"
        echo ✅ Debug APK已复制到: %APK_DEST%
    ) else (
        echo ❌ 找不到构建的APK文件
        pause
        exit /b 1
    )
)

echo.
echo 📤 步骤4: 复制到服务器目录...
if exist "..\源码\apk\" (
    copy "%APK_DEST%" "..\源码\apk\%APK_DEST%"
    echo ✅ APK已复制到服务器目录
) else (
    echo ⚠️ 服务器目录不存在，请手动复制APK文件
)

echo.
echo 🔍 步骤5: APK安全信息检查...
echo.
echo 📱 APK文件信息：
if exist "%APK_DEST%" (
    for %%A in ("%APK_DEST%") do (
        echo   文件大小: %%~zA 字节
        echo   修改时间: %%~tA
    )
) else (
    echo   ❌ APK文件不存在
)

echo.
echo ========================================
echo ✅ 防报毒APK构建完成！
echo ========================================
echo.
echo 🔒 安全优化特性：
echo   ✅ 网络安全配置 - 防止网络行为被误报
echo   ✅ 应用完整性检查 - 证明应用合法性
echo   ✅ 代码混淆保护 - 降低被分析风险
echo   ✅ 签名验证机制 - 防止篡改
echo   ✅ 权限最小化 - 减少敏感权限
echo   ✅ 安全日志记录 - 便于问题排查
echo.
echo 📱 安装测试步骤：
echo   1. 在不同品牌手机上测试安装
echo   2. 检查是否还有报毒提示
echo   3. 验证所有功能正常工作
echo   4. 测试合同页面和打印功能
echo.
echo 🛡️ 如果仍有报毒提示：
echo   1. 使用您的正式签名重新签名
echo   2. 考虑申请应用商店上架
echo   3. 联系安全软件厂商申请白名单
echo   4. 提供应用说明和企业资质
echo.
echo 📋 APK文件：%APK_DEST%
echo.
pause
