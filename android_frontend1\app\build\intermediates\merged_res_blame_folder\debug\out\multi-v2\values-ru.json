{"logs": [{"outputFile": "com.dailuanshej.loan.app-mergeDebugResources-27:/values-ru/values-ru.xml", "map": [{"source": "E:\\SDK\\caches\\transforms-3\\7cfde09bef56db81a739e41054cc6284\\transformed\\material-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,465,562,699,791,857,956,1033,1096,1214,1279,1336,1406,1467,1521,1637,1694,1756,1810,1884,2012,2100,2186,2323,2407,2492,2583,2637,2688,2754,2826,2904,3000,3080,3156,3233,3310,3399,3472,3562,3657,3731,3812,3905,3960,4026,4112,4197,4259,4323,4386,4484,4584,4679,4781,4839,4894", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,90,53,50,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,57,54,79", "endOffsets": "376,460,557,694,786,852,951,1028,1091,1209,1274,1331,1401,1462,1516,1632,1689,1751,1805,1879,2007,2095,2181,2318,2402,2487,2578,2632,2683,2749,2821,2899,2995,3075,3151,3228,3305,3394,3467,3557,3652,3726,3807,3900,3955,4021,4107,4192,4254,4318,4381,4479,4579,4674,4776,4834,4889,4969"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3232,3329,3466,3558,3624,3723,3800,3863,3981,4046,4103,4173,4234,4288,4404,4461,4523,4577,4651,4779,4867,4953,5090,5174,5259,5350,5404,5455,5521,5593,5671,5767,5847,5923,6000,6077,6166,6239,6329,6424,6498,6579,6672,6727,6793,6879,6964,7026,7090,7153,7251,7351,7446,7548,7606,7661", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,90,53,50,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,57,54,79", "endOffsets": "426,3227,3324,3461,3553,3619,3718,3795,3858,3976,4041,4098,4168,4229,4283,4399,4456,4518,4572,4646,4774,4862,4948,5085,5169,5254,5345,5399,5450,5516,5588,5666,5762,5842,5918,5995,6072,6161,6234,6324,6419,6493,6574,6667,6722,6788,6874,6959,7021,7085,7148,7246,7346,7441,7543,7601,7656,7736"}}, {"source": "E:\\SDK\\caches\\transforms-3\\ae146d59d58c77d62f24ab479ef1acb9\\transformed\\appcompat-1.5.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,7741", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,7818"}}, {"source": "E:\\SDK\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "93", "startColumns": "4", "startOffsets": "7823", "endColumns": "100", "endOffsets": "7919"}}]}]}