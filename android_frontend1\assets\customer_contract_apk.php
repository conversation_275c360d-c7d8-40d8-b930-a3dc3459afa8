<?php
/**
 * APK专用合同页面 - 解决印章显示和打印问题
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// 获取客户ID
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$is_user_mode = isset($_GET['user']) && $_GET['user'] == '1';

// 检测是否为APK访问
$is_apk = isset($_GET['apk']) && $_GET['apk'] == '1';

// 初始化客户数据库对象
require_once 'customer_data.php';
$customerDB = new CustomerData();

// 获取客户数据
if (!$is_user_mode) {
    $customer = $customerDB->getCustomerById($customer_id);
} else {
    $customer = $customerDB->getCustomerById($customer_id);
}

if (!$customer) {
    echo "客户不存在";
    exit;
}

// 计算借款信息
$loan_amount = isset($customer['loan_amount']) ? floatval($customer['loan_amount']) : 10000;
$loan_periods = isset($customer['loan_periods']) ? intval($customer['loan_periods']) : 12;
$monthly_rate = isset($customer['monthly_rate']) ? floatval($customer['monthly_rate']) : 0.02;

if ($loan_periods <= 0) $loan_periods = 12;
if ($monthly_rate <= 0) $monthly_rate = 0.02;
if ($loan_amount <= 0) $loan_amount = 10000;

$denominator = pow(1 + $monthly_rate, $loan_periods) - 1;
if ($denominator > 0) {
    $monthly_payment = $loan_amount * ($monthly_rate * pow(1 + $monthly_rate, $loan_periods)) / $denominator;
} else {
    $monthly_payment = $loan_amount / $loan_periods;
}

$total_amount = $monthly_payment * $loan_periods;

// 获取上传的印章图片路径
$seal_image_path = '';
if (isset($customer['lender_seal']) && !empty($customer['lender_seal'])) {
    $seal_image_path = $customer['lender_seal'];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>借款合同 - <?php echo htmlspecialchars($customer['customer_name']); ?></title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;

            /* APK横向容器修复 */
            width: 100%;
            max-width: 100vw;
            overflow-x: hidden;
            box-sizing: border-box;

            /* 强制文字水平方向 */
            writing-mode: horizontal-tb;
            direction: ltr;
            text-orientation: mixed;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border: 1px solid #ddd;

            /* APK横向容器优化 */
            width: 100%;
            box-sizing: border-box;
            overflow-x: hidden;
            word-wrap: break-word;
            word-break: break-all;

            /* 确保容器不超出屏幕 */
            min-width: 0;
            flex-shrink: 1;
        }

        .contract-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;

            /* APK表格横向优化 */
            table-layout: fixed;
            max-width: 100%;
            overflow-x: auto;
            box-sizing: border-box;
        }

        .info-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            vertical-align: top;

            /* APK单元格优化 */
            word-wrap: break-word;
            word-break: break-all;
            max-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;

            /* 强制文字水平 */
            writing-mode: horizontal-tb;
            direction: ltr;
        }

        .label {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 120px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }

        /* APK专用签字区域样式 */
        .signature-area {
            margin-top: 60px;
            min-height: 200px;
            clear: both;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 60px;
            table-layout: fixed;
        }

        .signature-table td {
            width: 50%;
            vertical-align: top;
            padding: 20px;
            border: none;
        }

        .borrower-signature {
            text-align: left;
        }

        .lender-signature {
            text-align: center;
        }

        .signature-line {
            border-bottom: 2px solid #000;
            height: 40px;
            width: 200px;
            margin-top: 20px;
        }

        /* APK专用印章样式 */
        .seal-container {
            margin-top: 20px;
            text-align: center;
        }

        .seal-image {
            max-width: 120px;
            max-height: 120px;
            width: 120px;
            height: 120px;
            opacity: 0.85;
        }

        .seal-svg {
            width: 120px;
            height: 120px;
            opacity: 0.85;
            display: block;
            margin: 0 auto;
        }

        .sign-date {
            text-align: center;
            margin-top: 40px;
            font-size: 16px;
        }

        /* APK专用按钮样式 */
        .apk-buttons {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .apk-btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 16px;
            border: none;
            cursor: pointer;
        }

        .apk-btn:hover {
            background-color: #0056b3;
        }

        .apk-btn.print {
            background-color: #28a745;
        }

        .apk-btn.print:hover {
            background-color: #1e7e34;
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .apk-buttons {
                display: none;
            }
            
            .container {
                border: none;
                box-shadow: none;
                max-width: none;
                margin: 0;
                padding: 20px;
            }
        }

        /* APK专用横向容器修复 */
        * {
            box-sizing: border-box;
            max-width: 100%;
            writing-mode: horizontal-tb !important;
            direction: ltr !important;
            text-orientation: mixed !important;
        }

        /* 防止横向溢出 */
        html, body {
            overflow-x: hidden;
            width: 100%;
            max-width: 100vw;
        }

        /* 表格响应式处理 */
        .info-table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }

        .info-table tbody,
        .info-table tr,
        .info-table td {
            display: block;
            width: 100%;
        }

        .info-table tr {
            border: 1px solid #ddd;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }

        .info-table td {
            border: none;
            padding: 5px 0;
            text-align: left;
            white-space: normal;
        }

        .info-table .label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
                font-size: 14px;
            }

            .container {
                padding: 15px;
                margin: 0;
                border: none;
                max-width: 100%;
                width: 100%;
            }

            .contract-title {
                font-size: 20px;
                margin-bottom: 20px;
            }

            .signature-table {
                display: block;
                width: 100%;
            }

            .signature-table td {
                display: block;
                width: 100% !important;
                padding: 15px 10px;
                text-align: center;
                border: none;
            }

            .borrower-signature {
                text-align: center;
                margin-bottom: 30px;
            }

            .signature-line {
                margin: 20px auto;
            }

            .seal-container {
                margin: 20px 0;
            }

            /* APK按钮移动端优化 */
            .apk-buttons {
                padding: 15px 10px;
            }

            .apk-btn {
                display: block;
                width: 100%;
                margin: 10px 0;
                padding: 15px;
                font-size: 16px;
            }
        }

        /* 超小屏幕适配 */
        @media (max-width: 480px) {
            body {
                padding: 5px;
                font-size: 12px;
            }

            .container {
                padding: 10px;
            }

            .contract-title {
                font-size: 18px;
            }

            .info-table td {
                font-size: 12px;
                padding: 3px 0;
            }

            .seal-svg,
            .seal-image {
                width: 80px !important;
                height: 80px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="contract-title">借款合同</div>

        <div class="section">
            <div class="section-title">借款人信息</div>
            <table class="info-table">
                <tr>
                    <td class="label">姓名</td>
                    <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                    <td class="label">身份证号</td>
                    <td><?php echo htmlspecialchars($customer['id_card']); ?></td>
                </tr>
                <tr>
                    <td class="label">手机号码</td>
                    <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                    <td class="label">银行卡号</td>
                    <td><?php echo htmlspecialchars($customer['bank_card']); ?></td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title">借款详情</div>
            <table class="info-table">
                <tr>
                    <td class="label">借款金额</td>
                    <td>人民币 <strong><?php echo number_format($loan_amount, 2); ?></strong> 元</td>
                    <td class="label">借款期限</td>
                    <td><?php echo $loan_periods; ?> 个月</td>
                </tr>
                <tr>
                    <td class="label">月利率</td>
                    <td><?php echo ($monthly_rate * 100); ?>%</td>
                    <td class="label">月还款额</td>
                    <td>人民币 <?php echo number_format($monthly_payment, 2); ?> 元</td>
                </tr>
                <tr>
                    <td class="label">总还款额</td>
                    <td>人民币 <?php echo number_format($total_amount, 2); ?> 元</td>
                    <td class="label">借款时间</td>
                    <td><?php echo htmlspecialchars(isset($customer['loan_time']) ? $customer['loan_time'] : date('Y-m-d')); ?></td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title">合同条款</div>
            <div style="line-height: 1.8; margin: 20px 0;">
                <p><strong>第一条 借款用途：</strong>甲方向乙方借款用于个人消费，包括但不限于购物、装修、旅游、教育等。</p>
                <p><strong>第二条 借款期限：</strong>自本合同生效之日起，至最后一期还款日止，共计 <?php echo $loan_periods; ?> 个月。</p>
                <p><strong>第三条 还款方式：</strong>甲方按月等额本息还款，每月还款金额为人民币 <?php echo number_format($monthly_payment, 2); ?> 元。</p>
                <p><strong>第四条 违约责任：</strong>甲方未按时足额还款，每逾期一日，按照应还款金额的0.05%支付违约金。</p>
                <p><strong>第五条 其他约定：</strong>本合同自双方签字（或盖章）之日起生效，具有法律效力。</p>
            </div>
        </div>

        <!-- APK专用签字区域 -->
        <div class="signature-area">
            <table class="signature-table">
                <tr>
                    <td class="borrower-signature">
                        <p style="margin-bottom: 30px; font-size: 16px;">
                            甲方（借款人）签字：
                        </p>
                        <div class="signature-line"></div>
                    </td>
                    <td class="lender-signature">
                        <p style="margin-bottom: 30px; font-size: 16px;">
                            乙方（贷款人）：随意花金融有限公司
                        </p>
                        <div class="seal-container">
                            <?php if (!empty($seal_image_path) && file_exists($seal_image_path)): ?>
                                <!-- 显示上传的印章图片 -->
                                <img src="<?php echo htmlspecialchars($seal_image_path); ?>" alt="合同专用章" class="seal-image">
                            <?php else: ?>
                                <!-- 显示默认SVG印章 -->
                                <svg class="seal-svg" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="60" cy="60" r="55" fill="none" stroke="#dc3545" stroke-width="3"/>
                                    <text x="60" y="40" text-anchor="middle" font-size="12" fill="#dc3545" font-weight="bold" font-family="Arial, sans-serif">
                                        随意花金融有限公司
                                    </text>
                                    <text x="60" y="80" text-anchor="middle" font-size="10" fill="#dc3545" font-family="Arial, sans-serif">合同专用章</text>
                                </svg>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
            </table>
            
            <div class="sign-date">
                <p>签署日期：<?php echo htmlspecialchars(isset($customer['sign_date']) ? $customer['sign_date'] : date('Y 年 m 月 d 日')); ?></p>
            </div>
        </div>

        <?php if ($is_apk): ?>
        <!-- APK专用按钮 -->
        <div class="apk-buttons">
            <button class="apk-btn print" onclick="printContract()">🖨️ 打印合同</button>
            <button class="apk-btn" onclick="shareContract()">📤 分享合同</button>
            <button class="apk-btn" onclick="goBack()">📋 返回</button>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // APK专用打印功能
        function printContract() {
            try {
                // 方法1: 尝试标准打印
                if (window.print) {
                    window.print();
                    return;
                }
                
                // 方法2: 创建新窗口打印
                const printWindow = window.open('', '_blank');
                printWindow.document.write(document.documentElement.outerHTML);
                printWindow.document.close();
                printWindow.print();
                
            } catch (error) {
                // 方法3: 显示打印提示
                alert('📱 APK打印说明\n\n由于Android系统限制，请使用以下方式打印：\n\n1️⃣ 点击"分享合同"保存为图片\n2️⃣ 使用系统截图功能\n3️⃣ 在浏览器中打开此页面进行打印\n\n💡 或者联系客服获取电子版合同');
            }
        }

        // APK专用分享功能
        function shareContract() {
            try {
                // 尝试调用Android分享功能
                if (window.Android && window.Android.shareContract) {
                    window.Android.shareContract(document.title, window.location.href);
                    return;
                }
                
                // 备用方案：复制链接
                const url = window.location.href;
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url).then(() => {
                        alert('✅ 合同链接已复制到剪贴板');
                    });
                } else {
                    alert('📋 合同链接：\n' + url + '\n\n请手动复制此链接');
                }
                
            } catch (error) {
                alert('📤 分享功能暂不可用\n\n请使用系统截图功能保存合同图片');
            }
        }

        // 返回功能
        function goBack() {
            try {
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    window.close();
                }
            } catch (error) {
                alert('请使用返回按钮或关闭页面');
            }
        }

        // APK横向容器修复函数
        function fixApkLayout() {
            console.log('🔧 开始APK横向容器修复');

            // 1. 强制所有元素水平方向
            const allElements = document.querySelectorAll('*');
            allElements.forEach(element => {
                element.style.writingMode = 'horizontal-tb';
                element.style.direction = 'ltr';
                element.style.textOrientation = 'mixed';
                element.style.boxSizing = 'border-box';
                element.style.maxWidth = '100%';
            });

            // 2. 修复表格横向溢出
            const tables = document.querySelectorAll('.info-table');
            tables.forEach(table => {
                table.style.width = '100%';
                table.style.tableLayout = 'fixed';
                table.style.overflowX = 'auto';

                // 修复表格单元格
                const cells = table.querySelectorAll('td');
                cells.forEach(cell => {
                    cell.style.wordWrap = 'break-word';
                    cell.style.wordBreak = 'break-all';
                    cell.style.overflow = 'hidden';
                    cell.style.textOverflow = 'ellipsis';
                });
            });

            // 3. 修复容器宽度
            const container = document.querySelector('.container');
            if (container) {
                container.style.width = '100%';
                container.style.maxWidth = '100vw';
                container.style.overflowX = 'hidden';
                container.style.boxSizing = 'border-box';
            }

            // 4. 修复签字区域
            const signatureTable = document.querySelector('.signature-table');
            if (signatureTable) {
                signatureTable.style.width = '100%';
                signatureTable.style.tableLayout = 'fixed';

                const signatureCells = signatureTable.querySelectorAll('td');
                signatureCells.forEach(cell => {
                    cell.style.width = '50%';
                    cell.style.boxSizing = 'border-box';
                });
            }

            // 5. 检测并修复横向滚动
            if (document.body.scrollWidth > window.innerWidth) {
                console.log('⚠️ 检测到横向滚动，进行修复');
                document.body.style.overflowX = 'hidden';
                document.documentElement.style.overflowX = 'hidden';

                // 强制调整所有可能超宽的元素
                const wideElements = document.querySelectorAll('*');
                wideElements.forEach(element => {
                    if (element.offsetWidth > window.innerWidth) {
                        element.style.width = '100%';
                        element.style.maxWidth = '100vw';
                    }
                });
            }

            console.log('✅ APK横向容器修复完成');
        }

        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ APK合同页面加载完成');

            // 立即执行APK布局修复
            fixApkLayout();

            // 检查印章显示
            const sealImage = document.querySelector('.seal-image');
            if (sealImage) {
                sealImage.onerror = function() {
                    console.log('⚠️ 印章图片加载失败，显示默认SVG印章');
                    this.style.display = 'none';
                    this.parentNode.innerHTML = `
                        <svg class="seal-svg" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="60" cy="60" r="55" fill="none" stroke="#dc3545" stroke-width="3"/>
                            <text x="60" y="40" text-anchor="middle" font-size="12" fill="#dc3545" font-weight="bold" font-family="Arial, sans-serif">
                                随意花金融有限公司
                            </text>
                            <text x="60" y="80" text-anchor="middle" font-size="10" fill="#dc3545" font-family="Arial, sans-serif">合同专用章</text>
                        </svg>
                    `;
                };
            }

            // 延迟再次修复（确保所有内容加载完成）
            setTimeout(fixApkLayout, 1000);
        });

        // 窗口大小改变时重新修复
        window.addEventListener('resize', function() {
            setTimeout(fixApkLayout, 100);
        });

        // 页面显示时重新修复（处理浏览器前进后退）
        window.addEventListener('pageshow', function() {
            setTimeout(fixApkLayout, 100);
        });
    </script>
</body>
</html>
