<?php
/**
 * hetonggengx - 客户借款合同页面
 * 纸质合同样式，无线框设计
 */

// 在开发阶段开启所有错误报告，方便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'customer_data.php';

// --- 2. 初始化变量 ---
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$is_user_mode = isset($_GET['user']) && $_GET['user'] == '1';
$save_feedback = ''; // 用于向用户显示保存结果

// --- 3. 权限检查 (管理员模式) ---
if (!$is_user_mode) {
    // 为方便测试，如果session不存在，暂时设置为已登录
    if (!isset($_SESSION['admin_logged_in'])) {
        $_SESSION['admin_logged_in'] = true;
    }
}

// --- 4. 初始化客户数据管理 (使用与客户编辑相同的方式) ---
$customerDB = new CustomerData();

// --- 5. 【使用客户编辑相同的保存方式】处理表单提交 (保存数据) ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_contract']) && !$is_user_mode) {
    if ($customer_id > 0) {
        try {
            // 获取表单数据（基础信息 + 合同专用信息）
            $update_data = array(
                // 基础客户信息
                'customer_name' => $_POST['customer_name'],
                'id_card' => $_POST['id_card'],
                'phone' => isset($_POST['phone']) ? $_POST['phone'] : '',
                'address' => isset($_POST['address']) ? $_POST['address'] : '',
                'loan_amount' => floatval($_POST['loan_amount']),
                'loan_periods' => intval($_POST['loan_periods']),
                'bank_card' => isset($_POST['bank_card']) ? $_POST['bank_card'] : '',
                'bank_name' => isset($_POST['bank_name']) ? $_POST['bank_name'] : '',
                'loan_status' => isset($_POST['loan_status']) ? $_POST['loan_status'] : '已放款',

                // 合同专用信息
                'contract_no' => isset($_POST['contract_no']) ? $_POST['contract_no'] : '',
                'party_a_name' => isset($_POST['party_a_name']) ? $_POST['party_a_name'] : '',
                'party_b_name' => isset($_POST['party_b_name']) ? $_POST['party_b_name'] : '',
                'company_name' => isset($_POST['company_name']) ? $_POST['company_name'] : '',
                'company_phone' => isset($_POST['company_phone']) ? $_POST['company_phone'] : '',
                'company_address' => isset($_POST['company_address']) ? $_POST['company_address'] : '',
                'company_manager' => isset($_POST['company_manager']) ? $_POST['company_manager'] : '',
                'sign_date' => isset($_POST['sign_date']) ? $_POST['sign_date'] : '',
                'loan_purpose' => isset($_POST['loan_purpose']) ? $_POST['loan_purpose'] : '',
                'repayment_method' => isset($_POST['repayment_method']) ? $_POST['repayment_method'] : '',
                'penalty_clause' => isset($_POST['penalty_clause']) ? $_POST['penalty_clause'] : '',
                'other_terms' => isset($_POST['other_terms']) ? $_POST['other_terms'] : '',
                'monthly_rate' => isset($_POST['monthly_rate']) ? floatval($_POST['monthly_rate']) : 0,

                // 逾期标记（独立功能）
                'overdue_mark' => isset($_POST['overdue_mark']) ? 1 : 0
            );

            // 使用CustomerData类保存（与客户编辑页面完全相同的方式）
            if ($customerDB->updateCustomer($customer_id, $update_data)) {
                $save_feedback = '<div class="save-message success">✅ 合同信息已成功保存！</div>';
            } else {
                $save_feedback = '<div class="save-message error">❌ 保存失败，请重试。</div>';
            }

        } catch (Exception $e) {
            $save_feedback = '<div class="save-message error">❌ 保存失败: ' . $e->getMessage() . '</div>';
        }
    } else {
        $save_feedback = '<div class="save-message error">❌ 保存失败: 无效的客户ID。</div>';
    }
}

// --- 7. 【使用客户编辑相同的数据获取方式】获取客户数据 ---
$customer = $customerDB->getCustomerById($customer_id);

if (!$customer) {
    // 如果客户不存在，创建默认数据
    $customer = array(
        // 基础客户信息
        'id' => $customer_id,
        'customer_name' => '客户' . $customer_id,
        'id_card' => '110101199001011234',
        'phone' => '***********',
        'address' => '北京市朝阳区测试地址',
        'loan_amount' => 50000.00,
        'loan_periods' => 12,
        'bank_card' => '6222021234567890123',
        'bank_name' => '中国工商银行',
        'loan_status' => '已放款',
        'created_time' => date('Y-m-d H:i:s'),

        // 合同专用信息
        'contract_no' => 'XD' . date('Ymd') . sprintf('%04d', $customer_id),
        'party_a_name' => '甲方（借款人）',
        'party_b_name' => '乙方（贷款人）',
        'company_name' => '随意花金融有限公司',
        'company_phone' => '************',
        'company_address' => '北京市朝阳区金融街123号',
        'company_manager' => '张经理',
        'sign_date' => date('Y年m月d日'),
        'loan_purpose' => '甲方向乙方借款用于个人消费，包括但不限于购物、装修、旅游、教育等。',
        'repayment_method' => '甲方按月等额本息还款，每月还款金额根据借款金额和期限计算。',
        'penalty_clause' => '甲方未按时足额还款，每逾期一日，按照应还款金额的0.05%支付违约金。',
        'other_terms' => '本合同自双方签字（或盖章）之日起生效，具有法律效力。',
        'monthly_rate' => 2.0,

        // 逾期标记（独立功能）
        'overdue_mark' => 0
    );
}

// 统一字段名（确保合同内容能正确显示客户编辑的数据）
$customer['name'] = $customer['customer_name'];
$customer['id_number'] = $customer['id_card']; // 兼容旧字段名

// 确保所有必要字段都存在
if (!isset($customer['address'])) {
    $customer['address'] = '地址信息待完善';
}

// 为了兼容性，设置一个空的contractContent数组（用于JavaScript显示）
$contractContent = array();

// 计算还款信息
$rates = array(3 => 0.025, 6 => 0.023, 12 => 0.02, 18 => 0.018, 24 => 0.016, 36 => 0.015);
$monthly_rate = isset($rates[$customer['loan_periods']]) ? $rates[$customer['loan_periods']] : 0.02;
$total_amount = $customer['loan_amount'] * (1 + $monthly_rate * $customer['loan_periods']);
$monthly_payment = $total_amount / $customer['loan_periods'];
$total_interest = $total_amount - $customer['loan_amount'];

// 生成合同编号
$contract_no = 'XD' . date('Ymd') . sprintf('%04d', $customer['id']);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="version" content="20250713-v2">
    <title>借款合同 - hetonggengx</title>
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: white !important;
            background-color: white !important;
            background-image: none !important;
            min-height: 100vh;
            line-height: 1.5;
            color: #333;
        }

        /* 强制覆盖任何可能的背景样式 */
        html {
            background: white !important;
            background-color: white !important;
        }

        /* 标签样式 */
        .label, td.label, .info-table .label {
            background: white !important;
            background-color: white !important;
            background-image: none !important;
            color: #333 !important;
        }

        /* 主容器 */
        .container {
            max-width: 700px;
            margin: 10px auto;
            background: white;
            padding: 30px;
            position: relative;
        }

        /* 合同头部 */
        .contract-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
        }

        .contract-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .contract-no {
            font-size: 14px;
            color: #666;
            font-weight: normal;
            padding: 5px 15px;
            display: inline-block;
        }

        /* 章节样式 */
        .section {
            margin-bottom: 20px;
            background: white;
            padding: 15px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            padding-left: 10px;
            padding: 10px;
            margin: -15px -15px 15px -15px;
        }

        /* 信息表格 */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .info-table td {
            padding: 8px 12px;
        }

        .info-table .label {
            font-weight: bold;
            width: 120px;
            text-align: left;
        }

        /* 签名区域 */
        .signature-area {
            display: table;
            width: 100%;
            margin-top: 30px;
            background: white;
            padding: 20px;
        }

        .signature-box {
            display: table-cell;
            width: 50%;
            text-align: center;
            padding: 0 20px;
        }

        .signature-line {
            height: 40px;
            margin-bottom: 10px;
            line-height: 40px;
            font-weight: bold;
            color: #333;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            margin: 4px;
            background: white;
            color: #333;
            text-decoration: none;
            display: inline-block;
            border: 1px solid #333;
            cursor: pointer;
            font-weight: normal;
            font-size: 14px;
            border-radius: 3px;
        }

        .btn:hover {
            background: #333;
            color: white;
        }

        .btn.btn-success {
            background: #28a745;
            color: white;
        }

        .btn.btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn.btn-warning {
            background: #ffc107;
            color: #212529;
        }

        /* 操作区域 */
        .actions {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            background: white;
            margin: 20px -30px -30px -30px;
            padding: 15px 30px 20px 30px;
        }

        /* 编辑表单样式 */
        #editForm {
            background: white;
            padding: 20px;
            margin-top: 20px;
        }

        #editForm h4 {
            margin: 15px 0 10px 0;
            padding: 8px 12px;
            color: #333;
            font-weight: bold;
        }

        #editForm input, #editForm select, #editForm textarea {
            border: none !important;
            border-bottom: 1px solid #ccc !important;
            background: transparent !important;
            border-radius: 0 !important;
        }

        #editForm input:focus, #editForm select:focus, #editForm textarea:focus {
            border-bottom-color: #333 !important;
            outline: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 5px;
                padding: 15px;
            }

            .contract-title {
                font-size: 20px;
            }

            .signature-area {
                display: block;
            }

            .signature-box {
                display: block;
                width: 100%;
                margin-bottom: 15px;
            }

            #editForm {
                padding: 15px;
            }
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
            }
            .actions, #editForm {
                display: none !important;
            }
            .container {
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }
            .section {
                background: white;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">


        <!-- 完整的小额贷款借款合同 -->
        <div class="contract-header">
            <div class="contract-title">
                小额贷款借款合同
                <?php if (isset($customer['overdue_mark']) && $customer['overdue_mark'] == 1): ?>
                    <span style="color: #dc3545; font-weight: bold; margin-left: 20px; font-size: 24px;">逾期</span>
                <?php endif; ?>
            </div>
            <div class="contract-no">合同编号：<?php echo htmlspecialchars(isset($customer['contract_no']) ? $customer['contract_no'] : $contract_no); ?></div>
        </div>

        <div class="section">
            <div class="section-title"><?php echo htmlspecialchars(isset($customer['party_a_name']) ? $customer['party_a_name'] : '甲方（借款人）'); ?>信息</div>
            <table class="info-table">
                <tr>
                    <td class="label">姓名</td>
                    <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                    <td class="label">身份证号</td>
                    <td><?php echo htmlspecialchars($customer['id_card']); ?></td>
                </tr>
                <tr>
                    <td class="label">联系电话</td>
                    <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                    <td class="label">联系地址</td>
                    <td><?php echo htmlspecialchars($customer['address']); ?></td>
                </tr>
                <tr>
                    <td class="label">银行卡号</td>
                    <td><?php echo htmlspecialchars(isset($customer['bank_card']) ? $customer['bank_card'] : ''); ?></td>
                    <td class="label">开户银行</td>
                    <td><?php echo htmlspecialchars(isset($customer['bank_name']) ? $customer['bank_name'] : ''); ?></td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title"><?php echo htmlspecialchars(isset($customer['party_b_name']) ? $customer['party_b_name'] : '乙方（贷款人）'); ?>信息</div>
            <table class="info-table">
                <tr>
                    <td class="label">公司名称</td>
                    <td><?php echo htmlspecialchars(isset($customer['company_name']) ? $customer['company_name'] : '随意花金融有限公司'); ?></td>
                    <td class="label">联系电话</td>
                    <td><?php echo htmlspecialchars(isset($customer['company_phone']) ? $customer['company_phone'] : '************'); ?></td>
                </tr>
                <tr>
                    <td class="label">公司地址</td>
                    <td><?php echo htmlspecialchars(isset($customer['company_address']) ? $customer['company_address'] : '北京市朝阳区金融街123号'); ?></td>
                    <td class="label">负责人</td>
                    <td><?php echo htmlspecialchars(isset($customer['company_manager']) ? $customer['company_manager'] : '张经理'); ?></td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title">借款详情</div>
            <table class="info-table">
                <tr>
                    <td class="label">借款金额</td>
                    <td>人民币 <strong><?php echo number_format($customer['loan_amount'], 2); ?></strong> 元</td>
                    <td class="label">借款期限</td>
                    <td><?php echo $customer['loan_periods']; ?> 个月</td>
                </tr>
                <tr>
                    <td class="label">月利率</td>
                    <td><?php echo ($monthly_rate * 100); ?>%</td>
                    <td class="label">总利息</td>
                    <td>人民币 <?php echo number_format($total_interest, 2); ?> 元</td>
                </tr>
                <tr>
                    <td class="label">月还款金额</td>
                    <td>人民币 <?php echo number_format($monthly_payment, 2); ?> 元</td>
                    <td class="label">总还款金额</td>
                    <td>人民币 <?php echo number_format($total_amount, 2); ?> 元</td>
                </tr>
                <tr>
                    <td class="label">借款状态</td>
                    <td colspan="3">
                        <?php
                        $status = isset($customer['loan_status']) ? $customer['loan_status'] : '已放款';
                        $status_color = ($status == '逾期') ? 'color: #dc3545; font-weight: bold;' : '';
                        ?>
                        <span style="<?php echo $status_color; ?>"><?php echo htmlspecialchars($status); ?></span>
                    </td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title">合同条款</div>
            <div style="line-height: 1.8; margin: 20px 0;">
                <p><strong>第一条 借款用途：</strong><?php echo htmlspecialchars(isset($customer['loan_purpose']) ? $customer['loan_purpose'] : '甲方向乙方借款用于个人消费，包括但不限于购物、装修、旅游、教育等。'); ?></p>
                <p><strong>第二条 借款期限：</strong>自本合同生效之日起，至最后一期还款日止，共计 <?php echo $customer['loan_periods']; ?> 个月。</p>
                <p><strong>第三条 还款方式：</strong><?php echo htmlspecialchars(isset($customer['repayment_method']) ? $customer['repayment_method'] : '甲方按月等额本息还款，每月还款金额为人民币 ' . number_format($monthly_payment, 2) . ' 元。'); ?></p>
                <p><strong>第四条 违约责任：</strong><?php echo htmlspecialchars(isset($customer['penalty_clause']) ? $customer['penalty_clause'] : '甲方未按时足额还款，每逾期一日，按照应还款金额的0.05%支付违约金。'); ?></p>
                <p><strong>第五条 其他约定：</strong><?php echo htmlspecialchars(isset($customer['other_terms']) ? $customer['other_terms'] : '本合同自双方签字（或盖章）之日起生效，具有法律效力。'); ?></p>
            </div>
        </div>

        <div class="signature-area" style="margin-top: 60px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 60px;">
                <div>
                    <p style="margin-bottom: 60px;">甲方（借款人）签字：_____________________</p>
                </div>
                <div>
                    <p style="margin-bottom: 60px;">乙方（贷款人）：随意花金融有限公司</p>
                    <p style="margin-bottom: 60px;">签字：_____________________</p>
                </div>
            </div>
            <p style="text-align: center;">签署日期：<?php echo htmlspecialchars(isset($customer['sign_date']) ? $customer['sign_date'] : date('Y 年 m 月 d 日')); ?></p>
        </div>

        <!-- 按钮组 -->
        <div class="actions">
            <button onclick="window.print()" class="btn">🖨️ 打印合同</button>
            <?php if (!$is_user_mode): ?>
                <button onclick="toggleEdit()" id="editBtn" class="btn btn-success">✏️ 编辑合同</button>
                <a href="customer_edit.php?id=<?php echo $customer_id; ?>" class="btn btn-warning">👤 客户编辑</a>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!$is_user_mode): ?>
    <!-- 编辑表单（移动到合同容器下面） -->
    <div id="editForm" style="display: none; max-width: 700px; margin: 10px auto;">
        <h3 style="text-align: center; color: #333; margin-bottom: 20px; font-size: 18px;">编辑合同信息</h3>
        <?php echo $save_feedback; ?>

        <form method="post" action="customer_contract.php?id=<?php echo $customer_id; ?>">
            <!-- 基础客户信息 -->
            <h4 style="color: #333; padding-bottom: 5px;">基础客户信息</h4>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">客户姓名:</label>
                    <input type="text" name="customer_name" value="<?php echo htmlspecialchars($customer['customer_name']); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;" required>
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">身份证号:</label>
                    <input type="text" name="id_card" value="<?php echo htmlspecialchars($customer['id_card']); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;" required>
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">联系电话:</label>
                    <input type="text" name="phone" value="<?php echo htmlspecialchars($customer['phone']); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;" required>
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">联系地址:</label>
                    <input type="text" name="address" value="<?php echo htmlspecialchars($customer['address']); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;" required>
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">银行卡号:</label>
                    <input type="text" name="bank_card" value="<?php echo htmlspecialchars(isset($customer['bank_card']) ? $customer['bank_card'] : ''); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">开户银行:</label>
                    <select name="bank_name" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                        <option value="">请选择银行</option>
                        <?php
                        $banks = ['中国工商银行', '中国建设银行', '中国农业银行', '中国银行', '招商银行', '交通银行', '中信银行', '光大银行', '华夏银行', '民生银行'];
                        foreach ($banks as $bank):
                        ?>
                            <option value="<?php echo $bank; ?>" <?php echo (isset($customer['bank_name']) && $customer['bank_name'] == $bank) ? 'selected' : ''; ?>>
                                <?php echo $bank; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <!-- 借款信息 -->
            <h4 style="color: #333; padding-bottom: 5px;">借款信息</h4>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">借款金额:</label>
                    <input type="number" name="loan_amount" value="<?php echo $customer['loan_amount']; ?>" step="0.01" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;" required>
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">借款期限:</label>
                    <select name="loan_periods" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;" required>
                        <?php foreach ([3, 6, 12, 18, 24, 36] as $period): ?>
                            <option value="<?php echo $period; ?>" <?php echo ($customer['loan_periods'] == $period) ? 'selected' : ''; ?>>
                                <?php echo $period; ?> 个月
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">月利率(%):</label>
                    <input type="number" name="monthly_rate" value="<?php echo isset($customer['monthly_rate']) ? $customer['monthly_rate'] : 2.0; ?>" step="0.1" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">放款状态:</label>
                    <select name="loan_status" id="loan_status_select" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;" onchange="updateStatusColor(this)">
                        <?php
                        $statuses = ['审核中', '已放款', '已还款', '逾期'];
                        foreach ($statuses as $status):
                        ?>
                            <option value="<?php echo $status; ?>" <?php echo (isset($customer['loan_status']) && $customer['loan_status'] == $status) ? 'selected' : ''; ?>
                                    <?php echo ($status == '逾期') ? 'style="color: #dc3545; font-weight: bold;"' : ''; ?>>
                                <?php echo $status; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">逾期标记:</label>
                    <div style="padding: 8px; background: white;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="checkbox" name="overdue_mark" value="1" <?php echo (isset($customer['overdue_mark']) && $customer['overdue_mark'] == 1) ? 'checked' : ''; ?> style="margin-right: 8px;">
                            <span style="color: #dc3545; font-weight: bold;">逾期</span>
                            <span style="margin-left: 10px; color: #666; font-size: 12px;">(勾选后将在合同标题显示红色"逾期"标记)</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 合同信息 -->
            <h4 style="color: #333; padding-bottom: 5px;">合同信息</h4>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">合同编号:</label>
                    <input type="text" name="contract_no" value="<?php echo htmlspecialchars(isset($customer['contract_no']) ? $customer['contract_no'] : $contract_no); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">签署日期:</label>
                    <input type="text" name="sign_date" value="<?php echo htmlspecialchars(isset($customer['sign_date']) ? $customer['sign_date'] : date('Y年m月d日')); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">甲方名称:</label>
                    <input type="text" name="party_a_name" value="<?php echo htmlspecialchars(isset($customer['party_a_name']) ? $customer['party_a_name'] : '甲方（借款人）'); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">乙方名称:</label>
                    <input type="text" name="party_b_name" value="<?php echo htmlspecialchars(isset($customer['party_b_name']) ? $customer['party_b_name'] : '乙方（贷款人）'); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
            </div>

            <!-- 乙方（公司）信息 -->
            <h4 style="color: #333; padding-bottom: 5px;">乙方（公司）信息</h4>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">公司名称:</label>
                    <input type="text" name="company_name" value="<?php echo htmlspecialchars(isset($customer['company_name']) ? $customer['company_name'] : '随意花金融有限公司'); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">公司电话:</label>
                    <input type="text" name="company_phone" value="<?php echo htmlspecialchars(isset($customer['company_phone']) ? $customer['company_phone'] : '************'); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">公司地址:</label>
                    <input type="text" name="company_address" value="<?php echo htmlspecialchars(isset($customer['company_address']) ? $customer['company_address'] : '北京市朝阳区金融街123号'); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">负责人:</label>
                    <input type="text" name="company_manager" value="<?php echo htmlspecialchars(isset($customer['company_manager']) ? $customer['company_manager'] : '张经理'); ?>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
            </div>

            <!-- 合同条款 -->
            <h4 style="color: #333; padding-bottom: 5px;">合同条款</h4>
            <div style="margin-bottom: 20px;">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">借款用途:</label>
                    <textarea name="loan_purpose" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; height: 60px; resize: vertical;"><?php echo htmlspecialchars(isset($customer['loan_purpose']) ? $customer['loan_purpose'] : '甲方向乙方借款用于个人消费，包括但不限于购物、装修、旅游、教育等。'); ?></textarea>
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">还款方式:</label>
                    <textarea name="repayment_method" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; height: 60px; resize: vertical;"><?php echo htmlspecialchars(isset($customer['repayment_method']) ? $customer['repayment_method'] : '甲方按月等额本息还款，每月还款金额为人民币 ' . number_format($monthly_payment, 2) . ' 元。'); ?></textarea>
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">违约责任:</label>
                    <textarea name="penalty_clause" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; height: 60px; resize: vertical;"><?php echo htmlspecialchars(isset($customer['penalty_clause']) ? $customer['penalty_clause'] : '甲方未按时足额还款，每逾期一日，按照应还款金额的0.05%支付违约金。'); ?></textarea>
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">其他约定:</label>
                    <textarea name="other_terms" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; height: 60px; resize: vertical;"><?php echo htmlspecialchars(isset($customer['other_terms']) ? $customer['other_terms'] : '本合同自双方签字（或盖章）之日起生效，具有法律效力。'); ?></textarea>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px;">
                <button type="submit" name="save_contract" class="btn btn-success">💾 保存合同</button>
                <button type="button" onclick="toggleEdit()" class="btn btn-secondary">❌ 取消编辑</button>
            </div>
        </form>
    </div>
    <?php endif; ?>



    <script>
        function toggleEdit() {
            const editForm = document.getElementById('editForm');
            const editBtn = document.getElementById('editBtn');

            if (editForm.style.display === 'none') {
                editForm.style.display = 'block';
                editBtn.textContent = '👁️ 查看合同';
                editForm.scrollIntoView({ behavior: 'smooth' });
            } else {
                editForm.style.display = 'none';
                editBtn.textContent = '✏️ 编辑合同';
                document.querySelector('.container').scrollIntoView({ behavior: 'smooth' });
            }
        }
    </script>
</body>
</html>


















