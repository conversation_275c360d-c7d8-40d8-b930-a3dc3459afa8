<?php
/**
 * 用户登录页面 - 手机号登录
 */
session_start();
require_once 'customer_data.php';

$message = '';
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : 'customer_contract_auto.php?user=1';

// 处理登录
if ($_POST) {
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    
    if (empty($phone)) {
        $message = '请输入手机号';
    } else {
        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            $message = '请输入正确的手机号格式';
        } else {
            // 查找客户
            $customerDB = new CustomerData();
            $customer = $customerDB->getCustomerByPhone($phone);
            
            if ($customer) {
                $_SESSION['user_phone'] = $phone;
                $_SESSION['user_name'] = $customer['customer_name'];
                $_SESSION['user_id'] = $customer['id'];
                
                // 重定向到合同页面
                $redirect_url = str_replace('?user=1', '', $redirect) . '?id=' . $customer['id'] . '&user=1';
                header('Location: ' . $redirect_url);
                exit;
            } else {
                $message = '未找到该手机号对应的客户信息';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户登录</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        
        .login-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 5px rgba(79, 172, 254, 0.3);
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #4facfe;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #3d8bfe;
        }
        
        .message {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
            text-align: center;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 class="login-title">📱 客户登录</h2>
        
        <?php if ($message): ?>
            <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <form method="post">
            <div class="form-group">
                <label for="phone">手机号</label>
                <input type="tel" id="phone" name="phone" placeholder="请输入您的手机号" required 
                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
            </div>
            
            <button type="submit" class="btn">登录查看合同</button>
        </form>
        
        <div class="info">
            <strong>说明：</strong><br>
            请输入您在办理贷款时留下的手机号码<br>
            登录后可查看您的借款合同
        </div>
    </div>
</body>
</html>
