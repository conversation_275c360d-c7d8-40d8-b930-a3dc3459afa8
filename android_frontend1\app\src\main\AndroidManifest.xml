<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dailuanshej.loan">

    <!-- 网络权限 - 必需 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 通讯录权限 - 仅在需要时申请 -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />

    <!-- 存储权限 - 兼容新旧版本 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- Android 13+ 新权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_DOCUMENTS" />

    <!-- 防止被识别为恶意软件的权限声明 -->
    <uses-permission android:name="android.permission.WAKE_LOCK"
        android:maxSdkVersion="22" />

    <!-- 明确声明不使用敏感权限 -->
    <uses-permission android:name="android.permission.CAMERA"
        android:required="false" />
    <uses-permission android:name="android.permission.RECORD_AUDIO"
        android:required="false" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"
        android:required="false" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"
        android:required="false" />

    <application
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config">
        
        <activity 
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
