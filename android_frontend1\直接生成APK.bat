@echo off
title 在E:\android_frontend生成APK
color 0A
echo.
echo ========================================
echo    直接在 E:\android_frontend 生成APK
echo ========================================
echo.

echo [1/4] 创建目标目录...
if not exist "E:\android_frontend" (
    mkdir "E:\android_frontend"
    echo ✅ 已创建目录: E:\android_frontend
) else (
    echo ℹ️  目录已存在: E:\android_frontend
)

echo.
echo [2/4] 复制项目文件...
echo 源路径: E:\仿随意花小贷源码完整版\android_frontend
echo 目标路径: E:\android_frontend
echo.

robocopy "E:\仿随意花小贷源码完整版\android_frontend" "E:\android_frontend" /E /NFL /NDL /NJH /NJS

echo ✅ 项目文件复制完成
echo.

echo [3/4] 切换到工作目录...
E:
cd \android_frontend

if exist "gradlew.bat" (
    echo ✅ 工作目录正确: %CD%
) else (
    echo ❌ 工作目录错误，gradlew.bat未找到
    pause
    exit /b 1
)

echo.
echo [4/4] 开始构建APK...
echo 🔥 这将需要几分钟时间，请耐心等待...
echo.

gradlew.bat clean
gradlew.bat assembleDebug

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo        🎉 构建成功！APK已生成
    echo ========================================
    echo.
    
    for /r "app\build\outputs\apk" %%f in (*.apk) do (
        echo 📱 APK文件: %%f
        echo.
        
        :: 复制到桌面
        copy "%%f" "%USERPROFILE%\Desktop\SuiYiHua-Final.apk" >nul 2>&1
        
        if exist "%USERPROFILE%\Desktop\SuiYiHua-Final.apk" (
            echo ✅ APK已复制到桌面: SuiYiHua-Final.apk
        )
        
        echo.
        echo 📂 项目位置: E:\android_frontend
        echo 📱 APK位置: %%f
        echo 🖥️  桌面备份: %USERPROFILE%\Desktop\SuiYiHua-Final.apk
        echo.
        echo 🚀 现在可以测试APK了！
        echo.
        echo 测试步骤：
        echo 1. 将APK传输到手机
        echo 2. 安装APK（允许未知来源）
        echo 3. 测试通讯录功能
        echo 4. 使用 adb logcat -s SuiYiHua 查看日志
        goto :success
    )
    
    echo ❌ 构建成功但未找到APK文件
    
) else (
    echo.
    echo ========================================
    echo         ❌ 构建失败
    echo ========================================
    echo.
    echo 错误代码: %ERRORLEVEL%
    echo.
    echo 💡 解决方案：
    echo 1. 运行 fix_java.bat 配置JDK
    echo 2. 检查网络连接
    echo 3. 检查Java安装
    echo 4. 重启电脑后重试
)

:success
echo.
echo ========================================
echo 按任意键退出...
pause >nul
