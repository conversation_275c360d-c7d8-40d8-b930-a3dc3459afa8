@echo off
echo ========================================
echo 🔒 创建APK签名证书 - 防止报毒
echo ========================================

echo.
echo 📋 证书信息配置：
echo   应用名称：随意花借贷
echo   公司名称：随意花金融有限公司
echo   域名：dailuanshej.cn
echo   有效期：25年
echo.

set KEYSTORE_FILE=youyihua-release.keystore
set KEY_ALIAS=youyihua
set KEYSTORE_PASSWORD=youyihua2025!@#
set KEY_PASSWORD=youyihua2025!@#

echo 🔑 开始创建签名证书...
echo.

keytool -genkey -v ^
    -keystore %KEYSTORE_FILE% ^
    -alias %KEY_ALIAS% ^
    -keyalg RSA ^
    -keysize 2048 ^
    -validity 9125 ^
    -storepass %KEYSTORE_PASSWORD% ^
    -keypass %KEY_PASSWORD% ^
    -dname "CN=随意花金融有限公司, OU=技术部, O=随意花金融有限公司, L=北京, ST=北京, C=CN"

if %errorlevel% neq 0 (
    echo ❌ 证书创建失败
    echo.
    echo 💡 请确保已安装Java JDK并配置环境变量
    echo    下载地址：https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo.
echo ✅ 签名证书创建成功！
echo.
echo 📁 证书文件：%KEYSTORE_FILE%
echo 🔑 证书别名：%KEY_ALIAS%
echo 🔒 证书密码：%KEYSTORE_PASSWORD%
echo.

echo 🔍 验证证书信息...
keytool -list -v -keystore %KEYSTORE_FILE% -storepass %KEYSTORE_PASSWORD%

echo.
echo ========================================
echo ✅ 证书创建完成！
echo ========================================
echo.
echo 📋 下一步：
echo   1. 运行 sign-apk.bat 对APK进行签名
echo   2. 签名后的APK将大大减少报毒风险
echo   3. 建议申请Google Play Console开发者账号
echo.
pause
