# 📱 APK测试完整流程

## 快速开始

### 1. 编译APK
双击运行：`编译测试APK.bat`

这个脚本会：
- ✅ 检查项目结构
- ✅ 复制调试文件到assets
- ✅ 清理旧构建
- ✅ 编译新APK
- ✅ 自动安装到手机（如果连接）

### 2. 安装APK到手机
- **自动安装**：如果手机通过USB连接并开启调试模式
- **手动安装**：复制 `app\build\outputs\apk\debug\app-debug.apk` 到手机安装

### 3. 测试权限功能

#### 方法1：使用专门的调试页面（推荐）
在APP地址栏输入：
```
file:///android_asset/debug_test.html
```

按顺序测试：
1. **检查权限状态** - 查看当前权限情况
2. **直接权限申请** - 🔥 **重点测试这个**
3. **强制权限弹窗** - 如果上面失败
4. **测试JS接口** - 验证接口连接
5. **读取通讯录** - 测试权限是否生效

#### 方法2：访问网站测试
直接访问：`https://dailuanshej.cn`
然后点击通讯录相关功能

### 4. 查看调试日志
同时运行：`监控ADB日志.bat`
或手动执行：
```bash
adb logcat -s SuiYiHua
```

## 预期测试结果

### 正常情况应该看到：
1. **权限状态检查** → 显示"未授权"
2. **直接权限申请** → 出现权限说明对话框
3. **点击确定** → 系统权限弹窗出现
4. **点击允许** → 权限授予成功
5. **读取通讯录** → 成功获取联系人数据

### 如果出现问题：
- **JS接口不存在** → 检查WebView设置
- **权限弹窗不出现** → 查看ADB日志
- **点击无响应** → 检查回调处理
- **权限被永久拒绝** → 引导到设置页面

## 常见问题解决

### 问题1：编译失败
- 检查Android SDK路径
- 确保gradlew.bat可执行
- 查看错误信息

### 问题2：APK无法安装
- 检查手机USB调试模式
- 手动安装APK文件
- 检查应用签名

### 问题3：权限弹窗无响应
- 使用调试页面逐步测试
- 查看ADB日志详细信息
- 尝试手动重置APP权限

### 问题4：通讯录读取失败
- 确认权限已正确授予
- 检查系统权限设置
- 查看JS接口调用日志

## 调试技巧

### 实时日志监控
```bash
# 监控所有日志
adb logcat

# 只监控APP日志
adb logcat -s SuiYiHua

# 保存日志到文件
adb logcat -s SuiYiHua > debug.log
```

### 权限状态查询
```bash
# 查看APP权限状态
adb shell dumpsys package com.dailuanshej.loan | grep permission

# 手动授权权限
adb shell pm grant com.dailuanshej.loan android.permission.READ_CONTACTS

# 重置APP权限
adb shell pm reset-permissions com.dailuanshej.loan
```

### WebView调试
在Chrome中访问：`chrome://inspect`
选择你的设备和WebView进行调试

## 反馈信息模板

如果测试后仍有问题，请提供：

```
手机信息：
- 型号：[如 小米12]
- Android版本：[如 Android 12]
- MIUI版本：[如适用]

测试结果：
- 调试页面状态：[复制状态信息]
- 卡在哪一步：[具体描述]
- 错误现象：[权限弹窗不出现/点击无响应/其他]

ADB日志：
[粘贴相关日志]
```

这样可以更精确地定位和解决问题。
