@echo off
echo ========================================
echo 🔧 测试编译 - 验证代码修复
echo ========================================

echo.
echo 📋 检查项目：
echo   • MainActivity.java 导入修复
echo   • SecurityHelper.java 安全类
echo   • ProGuard 混淆配置
echo   • 网络安全配置
echo.

echo 🔧 步骤1: 清理项目...
call gradlew clean
if %errorlevel% neq 0 (
    echo ❌ Clean 失败
    pause
    exit /b 1
)

echo.
echo 🔧 步骤2: 编译检查...
call gradlew compileDebugJavaWithJavac
if %errorlevel% neq 0 (
    echo ❌ 编译失败，请检查代码错误
    echo.
    echo 💡 常见问题：
    echo   1. 检查导入语句是否完整
    echo   2. 检查类名和方法名是否正确
    echo   3. 检查语法错误
    pause
    exit /b 1
)

echo.
echo ✅ 编译检查通过！

echo.
echo 🔧 步骤3: 构建Debug APK...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ APK构建失败
    pause
    exit /b 1
)

echo.
echo ✅ APK构建成功！

echo.
echo 📦 步骤4: 检查APK文件...
set APK_FILE=app\build\outputs\apk\debug\app-debug.apk
if exist "%APK_FILE%" (
    echo ✅ APK文件存在: %APK_FILE%
    for %%A in ("%APK_FILE%") do (
        echo   文件大小: %%~zA 字节
        echo   修改时间: %%~tA
    )
) else (
    echo ❌ APK文件不存在
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ 编译测试完成！
echo ========================================
echo.
echo 📋 验证结果：
echo   ✅ 代码编译通过
echo   ✅ APK构建成功
echo   ✅ 导入错误已修复
echo   ✅ 安全功能已集成
echo.
echo 🚀 下一步：
echo   1. 运行 build-secure-apk.bat 构建正式版本
echo   2. 使用您的签名对APK进行签名
echo   3. 在不同设备上测试安装
echo.
pause
