<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字方向测试</title>
    <style>
        /* 确保所有文字都是横排显示 - 强制覆盖 */
        *, *::before, *::after {
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }
        
        /* 特别针对文字内容的元素 */
        span, div, p, h1, h2, h3, h4, h5, h6, td, th, li, a, label, input, textarea, select {
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            text-align: left !important;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: #f5f5f5;
            color: #333;
            font-size: 14px;
            line-height: 1.4;
            padding: 20px;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }
        
        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 13px;
            min-width: 80px;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            display: inline-block !important;
            vertical-align: top !important;
            text-align: left !important;
        }
        
        .info-value {
            color: #333;
            font-size: 14px;
            text-align: right;
            flex: 1;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            display: inline-block !important;
            vertical-align: top !important;
        }
        
        .test-link {
            display: block;
            padding: 15px;
            background: #007aff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
        }
        
        h2, h3 {
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>📱 文字方向测试页面</h2>
        <p>这个页面用来测试移动端合同页面的文字是否正确横排显示。</p>
        
        <h3>👤 测试信息显示</h3>
        <div class="info-row">
            <span class="info-label">姓名</span>
            <span class="info-value">张三</span>
        </div>
        <div class="info-row">
            <span class="info-label">身份证号</span>
            <span class="info-value">110101199001011234</span>
        </div>
        <div class="info-row">
            <span class="info-label">电话号码</span>
            <span class="info-value">13800138001</span>
        </div>
        <div class="info-row">
            <span class="info-label">联系地址</span>
            <span class="info-value">北京市朝阳区测试地址123号</span>
        </div>
        <div class="info-row">
            <span class="info-label">银行卡号</span>
            <span class="info-value">6222021234567890123</span>
        </div>
        
        <h3>🔗 合同页面链接</h3>
        <a href="customer_contract_mobile.php?id=1" class="test-link">
            📱 移动端合同页面
        </a>
        <a href="customer_contract_mobile_h5.php?id=1" class="test-link">
            📱 H5优化版合同页面
        </a>
    </div>
    
    <div class="test-container">
        <h3>📋 检查清单</h3>
        <p>请检查以下内容是否横排显示：</p>
        <ul>
            <li>✅ 姓名：张三</li>
            <li>✅ 身份证号：110101199001011234</li>
            <li>✅ 电话号码：13800138001</li>
            <li>✅ 联系地址：北京市朝阳区测试地址123号</li>
            <li>✅ 银行卡号：6222021234567890123</li>
        </ul>
        <p><strong>如果上面的信息都是横排显示的，说明CSS修复成功！</strong></p>
    </div>
    
    <script>
        console.log('📱 文字方向测试页面已加载');
        console.log('🔧 检查所有文字是否横排显示');
        
        // 检查页面中的文字方向
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.info-label, .info-value');
            elements.forEach(function(el) {
                const style = window.getComputedStyle(el);
                console.log('元素:', el.textContent, '文字方向:', style.writingMode, style.direction);
            });
        });
    </script>
</body>
</html>
