<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Contacts Test</title>
    <style>
        body { font-family: sans-serif; padding: 1em; background-color: #f0f0f0; }
        .container { max-width: 600px; margin: auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2 { color: #333; }
        button { display: block; width: 100%; padding: 15px; margin-bottom: 10px; font-size: 16px; color: white; border: none; border-radius: 5px; cursor: pointer; }
        #btnRequest { background-color: #007bff; }
        #btnRead { background-color: #28a745; }
        #btnCheck { background-color: #ffc107; color: #333;}
        #log { margin-top: 20px; padding: 10px; background-color: #e9ecef; border-radius: 5px; height: 200px; overflow-y: scroll; white-space: pre-wrap; font-family: monospace; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1>终极通讯录测试</h1>
        <p>这个页面用于最终诊断通讯录的权限和读取问题。</p>
        
        <button id="btnCheck">1. 检查权限状态 (checkPermissionStatus)</button>
        <button id="btnRequest">2. 请求通讯录权限 (requestContactsPermission)</button>
        <button id="btnRead">3. 强制读取通讯录 (forceReadContacts)</button>
        <button id="btnDirectRequest">4. 终极权限弹窗测试 (directPermissionRequest)</button>

        <h2>日志输出:</h2>
        <div id="log"></div>
    </div>

    <script>
        const logDiv = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const p = document.createElement('p');
            p.className = type;
            p.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(p);
            logDiv.scrollTop = logDiv.scrollHeight;
            // 同时输出到控制台
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function getInterface() {
            if (typeof window.AndroidInterface !== 'undefined') {
                return window.AndroidInterface;
            }
            if (typeof window.AndroidContacts !== 'undefined') {
                // 兼容旧版
                window.AndroidInterface = window.AndroidContacts;
                log('兼容模式：将 AndroidContacts 赋值给 AndroidInterface', 'info');
                return window.AndroidInterface;
            }
            return null;
        }

        function checkInterface() {
            const androidInterface = getInterface();
            if (androidInterface) {
                log('成功: Android JS接口已找到。', 'success');
                return true;
            } else {
                log('失败: 未找到 "AndroidInterface" 或 "AndroidContacts" JS接口。请确认App代码是否正确注入。', 'error');
                return false;
            }
        }
        
        // --- Button Listeners ---

        document.getElementById('btnCheck').addEventListener('click', () => {
            log('开始检查权限状态...');
            const androidInterface = getInterface();
            if (androidInterface) {
                try {
                    // 这个方法在Java中会返回一个详细的状态字符串
                    androidInterface.checkPermissionStatus();
                    log('已调用 checkPermissionStatus()，请查看App的Toast提示获取详细状态。');
                } catch (e) {
                    log(`检查权限时出错: ${e.message}`, 'error');
                }
            }
        });

        document.getElementById('btnRequest').addEventListener('click', () => {
            log('请求通讯录权限 (requestContactsPermission)...');
            const androidInterface = getInterface();
            if (androidInterface) {
                try {
                    androidInterface.requestContactsPermission();
                    log('权限请求已发送。请在App弹窗中操作。');
                } catch (e) {
                    log(`调用 requestContactsPermission 时出错: ${e.message}`, 'error');
                }
            }
        });

        document.getElementById('btnRead').addEventListener('click', () => {
            log('尝试强制读取通讯录 (forceReadContacts)...');
            const androidInterface = getInterface();
            if (androidInterface) {
                try {
                    androidInterface.forceReadContacts();
                    log('强制读取通讯录的命令已发送。');
                } catch (e) {
                    log(`调用 forceReadContacts 时出错: ${e.message}`, 'error');
                }
            }
        });

        document.getElementById('btnDirectRequest').addEventListener('click', () => {
            log('终极权限弹窗测试 (directPermissionRequest)...');
            const androidInterface = getInterface();
            if (androidInterface) {
                try {
                    androidInterface.directPermissionRequest();
                    log('终极权限弹窗测试命令已发送。');
                } catch (e) {
                    log(`调用 directPermissionRequest 时出错: ${e.message}`, 'error');
                }
            }
        });

        // --- Callback Functions from Android ---

        // 这个是关键的回调函数，Java代码在读取成功后会调用它
        window.onContactsResult = function(contactsJson) {
            log('安卓回调: 成功接收到通讯录数据。', 'success');
            try {
                const contacts = JSON.parse(contactsJson);
                if (contacts.length > 0) {
                    log(`共读取到 ${contacts.length} 位联系人。`);
                    log('前5位联系人:');
                    contacts.slice(0, 5).forEach(c => {
                        log(`  - ${c.name}: ${c.phone}`);
                    });
                    if (contacts.length > 5) {
                        log('...');
                    }
                } else {
                    log('读取成功，但通讯录为空或没有有效联系人。', 'info');
                }
            } catch (e) {
                log(`解析通讯录JSON时出错: ${e.message}`, 'error');
                log(`原始JSON数据 (前500字符): ${contactsJson.substring(0, 500)}`);
            }
        };

        // 权限结果回调
        window.onContactsPermissionResult = function(granted) {
            if (granted) {
                log('安卓回调: 权限已授予！现在可以点击读取按钮了。', 'success');
            } else {
                log('安卓回调: 权限被拒绝。', 'error');
            }
        };
        
        window.onContactsError = function(errorMessage) {
            log(`安卓回调: 发生错误 - ${errorMessage}`, 'error');
        };

        // Initial check
        window.addEventListener('load', () => {
            log('页面加载完成。等待操作...');
            // 延迟一点时间再检查，确保Android接口完全注入
            setTimeout(checkInterface, 500);
        });

    </script>
</body>
</html>
