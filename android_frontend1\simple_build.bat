@echo off
echo ========================================
echo        Simple APK Builder
echo ========================================
echo.

:: Get short path format to avoid Chinese path issues
for %%i in ("E:\仿随意花小贷源码完整版\android_frontend") do set "PROJECT_PATH=%%~si"

echo Project path: %PROJECT_PATH%
echo.

:: Change to project directory
cd /d "%PROJECT_PATH%"

if %ERRORLEVEL% neq 0 (
    echo ERROR: Cannot access project directory
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

:: Clean and build
echo Cleaning old build...
gradlew.bat clean

echo.
echo Building APK...
gradlew.bat assembleDebug

if %ERRORLEVEL% equ 0 (
    echo.
    echo BUILD SUCCESS!
    echo.
    
    :: Find APK file
    for /r "app\build\outputs\apk" %%f in (*.apk) do (
        echo APK: %%f
        copy "%%f" "%USERPROFILE%\Desktop\SuiYiHua.apk"
        echo Copied to Desktop: SuiYiHua.apk
        goto :done
    )
    
    echo No APK found
    
) else (
    echo BUILD FAILED!
    echo Check the error messages above
)

:done
pause
