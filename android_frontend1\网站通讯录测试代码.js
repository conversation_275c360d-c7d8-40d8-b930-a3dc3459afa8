// ============================================
// 随意花APP通讯录测试代码
// 将此代码添加到 https://dailuanshej.cn 页面中
// ============================================

// 1. 检测是否在APP环境中
function isInApp() {
    return typeof AndroidContacts !== 'undefined';
}

// 2. 显示测试控制面板
function createContactsTestPanel() {
    // 如果面板已存在，先移除
    const existingPanel = document.getElementById('contacts-test-panel');
    if (existingPanel) {
        existingPanel.remove();
    }
    
    // 创建测试面板
    const panel = document.createElement('div');
    panel.id = 'contacts-test-panel';
    panel.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        width: 300px;
        background: white;
        border: 2px solid #007bff;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 9999;
        font-family: Arial, sans-serif;
        font-size: 14px;
    `;
    
    const isApp = isInApp();
    
    panel.innerHTML = `
        <div style="margin-bottom: 10px; font-weight: bold; color: #007bff;">
            📱 通讯录功能测试
        </div>
        
        <div style="margin-bottom: 10px; padding: 8px; background: ${isApp ? '#d4edda' : '#f8d7da'}; border-radius: 5px; color: ${isApp ? '#155724' : '#721c24'};">
            状态: ${isApp ? '✅ APP环境' : '❌ 网页环境'}
        </div>
        
        ${isApp ? `
            <button onclick="testContactsPermission()" style="width: 100%; margin-bottom: 8px; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                🔐 检查权限
            </button>
            
            <button onclick="testContactsRead()" style="width: 100%; margin-bottom: 8px; padding: 10px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                📞 读取通讯录
            </button>
            
            <button onclick="testPermissionDialog()" style="width: 100%; margin-bottom: 8px; padding: 10px; background: #ffc107; color: #212529; border: none; border-radius: 5px; cursor: pointer;">
                🔧 测试权限对话框
            </button>
        ` : `
            <div style="padding: 8px; background: #fff3cd; border-radius: 5px; color: #856404;">
                请在随意花APP中打开此页面进行测试
            </div>
        `}
        
        <button onclick="hideContactsTestPanel()" style="width: 100%; padding: 8px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 10px;">
            关闭面板
        </button>
        
        <div id="test-log" style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; max-height: 200px; overflow-y: auto; font-size: 12px; display: none;">
            <div style="font-weight: bold; margin-bottom: 5px;">📝 测试日志:</div>
            <div id="log-content"></div>
        </div>
    `;
    
    document.body.appendChild(panel);
}

// 3. 隐藏测试面板
function hideContactsTestPanel() {
    const panel = document.getElementById('contacts-test-panel');
    if (panel) {
        panel.remove();
    }
}

// 4. 日志记录
function addTestLog(message, type = 'info') {
    const logDiv = document.getElementById('test-log');
    const logContent = document.getElementById('log-content');
    
    if (logDiv && logContent) {
        logDiv.style.display = 'block';
        
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.style.cssText = `
            margin-bottom: 3px; 
            padding: 3px 5px; 
            border-radius: 3px;
            background: ${type === 'error' ? '#f8d7da' : type === 'success' ? '#d4edda' : '#e2e3e5'};
            color: ${type === 'error' ? '#721c24' : type === 'success' ? '#155724' : '#383d41'};
        `;
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
    }
    
    console.log(`[通讯录测试] ${message}`);
}

// 5. 权限检查测试
function testContactsPermission() {
    addTestLog('开始检查通讯录权限...', 'info');
    
    if (typeof AndroidContacts !== 'undefined' && AndroidContacts.checkPermission) {
        AndroidContacts.checkPermission();
        addTestLog('权限检查请求已发送', 'info');
    } else {
        addTestLog('错误: AndroidContacts.checkPermission 不可用', 'error');
    }
}

// 6. 通讯录读取测试
function testContactsRead() {
    addTestLog('开始读取通讯录...', 'info');
    
    if (typeof AndroidContacts !== 'undefined' && AndroidContacts.requestContacts) {
        AndroidContacts.requestContacts();
        addTestLog('通讯录读取请求已发送', 'info');
    } else {
        addTestLog('错误: AndroidContacts.requestContacts 不可用', 'error');
    }
}

// 7. 权限对话框测试
function testPermissionDialog() {
    addTestLog('测试权限对话框响应...', 'info');
    
    if (typeof AndroidContacts !== 'undefined') {
        // 先检查权限状态
        AndroidContacts.checkPermission();
        
        // 2秒后请求权限（模拟用户操作）
        setTimeout(() => {
            addTestLog('开始权限请求测试...', 'info');
            AndroidContacts.requestContacts();
        }, 2000);
    } else {
        addTestLog('错误: AndroidContacts 接口不可用', 'error');
    }
}

// 8. 权限结果回调
window.onContactsPermissionResult = function(hasPermission) {
    const status = hasPermission ? '已授权' : '未授权';
    const type = hasPermission ? 'success' : 'error';
    
    addTestLog(`权限检查结果: ${status}`, type);
    
    // 更新页面显示
    updatePermissionStatus(hasPermission);
};

// 9. 通讯录数据回调
window.onContactsResult = function(contactsJson) {
    addTestLog('收到通讯录数据回调', 'success');
    
    try {
        const contacts = JSON.parse(contactsJson);
        addTestLog(`成功解析 ${contacts.length} 个联系人`, 'success');
        
        // 显示部分联系人数据
        displayContactsData(contacts);
        
    } catch (e) {
        addTestLog(`数据解析失败: ${e.message}`, 'error');
    }
};

// 10. 显示联系人数据
function displayContactsData(contacts) {
    // 如果已有联系人显示区域，先移除
    const existingDisplay = document.getElementById('contacts-display');
    if (existingDisplay) {
        existingDisplay.remove();
    }
    
    const display = document.createElement('div');
    display.id = 'contacts-display';
    display.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80%;
        max-width: 500px;
        max-height: 70%;
        background: white;
        border: 2px solid #28a745;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        overflow-y: auto;
    `;
    
    const contactList = contacts.slice(0, 10).map(contact => 
        `<div style="padding: 8px; border-bottom: 1px solid #eee;">
            <strong>${contact.name || '未知'}</strong><br>
            <span style="color: #666;">${contact.phone || '无号码'}</span>
        </div>`
    ).join('');
    
    display.innerHTML = `
        <div style="margin-bottom: 15px; font-weight: bold; color: #28a745; font-size: 16px;">
            📞 通讯录数据 (显示前10个)
        </div>
        
        <div style="margin-bottom: 15px; padding: 10px; background: #d4edda; border-radius: 5px; color: #155724;">
            总联系人数量: ${contacts.length}
        </div>
        
        <div style="margin-bottom: 15px;">
            ${contactList}
            ${contacts.length > 10 ? `<div style="padding: 8px; color: #666; text-align: center;">...还有 ${contacts.length - 10} 个联系人</div>` : ''}
        </div>
        
        <button onclick="closeContactsDisplay()" style="width: 100%; padding: 10px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
            关闭
        </button>
    `;
    
    document.body.appendChild(display);
}

// 11. 关闭联系人显示
function closeContactsDisplay() {
    const display = document.getElementById('contacts-display');
    if (display) {
        display.remove();
    }
}

// 12. 更新权限状态显示
function updatePermissionStatus(hasPermission) {
    // 可以在这里更新页面上的权限状态显示
    console.log('权限状态更新:', hasPermission);
}

// 13. 页面加载完成后自动显示测试面板
document.addEventListener('DOMContentLoaded', function() {
    // 延迟显示，确保页面完全加载
    setTimeout(() => {
        createContactsTestPanel();
        addTestLog('通讯录测试面板已加载', 'success');
    }, 1000);
});

// 14. 全局访问函数
window.showContactsTest = createContactsTestPanel;
window.hideContactsTest = hideContactsTestPanel;

// 15. 控制台提示
console.log('🚀 随意花通讯录测试工具已加载');
console.log('📱 如需手动显示测试面板，请调用: showContactsTest()');
console.log('❌ 如需隐藏测试面板，请调用: hideContactsTest()');

// ============================================
// 使用说明:
// 1. 将此代码添加到您的网站页面中
// 2. 在随意花APP中打开 https://dailuanshej.cn
// 3. 页面加载后会自动显示测试面板
// 4. 点击按钮测试各种通讯录功能
// ============================================
