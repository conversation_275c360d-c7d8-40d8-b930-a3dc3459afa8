<?php
/**
 * 前端用户登录后的主页 - 模拟原始ThinkPHP功能
 */

require_once 'customer_data.php';

// 获取用户手机号
$user_phone = isset($_GET['phone']) ? $_GET['phone'] : '';

if (!$user_phone) {
    header('Location: index.php');
    exit;
}

// 从客户数据中获取真实用户信息
$customerDB = new CustomerData();
$customers = $customerDB->getAllCustomers();
$user_info = null;

// 根据手机号查找用户
foreach ($customers as $customer) {
    if ($customer['phone'] == $user_phone) {
        $user_info = $customer;
        break;
    }
}

// 如果没找到用户，使用默认数据
if (!$user_info) {
    $user_info = array(
        'phone' => $user_phone,
        'customer_name' => '用户',
        'id_card' => '',
        'bank_card' => '',
        'bank_name' => '',
        'loan_amount' => 0,
        'loan_periods' => 0,
        'loan_status' => '未申请',
        'loan_time' => '',
        'loan_start_date' => '',
        'loan_end_date' => '',
        'created_time' => date('Y-m-d H:i:s')
    );
}

// 调试信息 - 显示实际读取的数据
// echo "<pre>Debug: " . print_r($user_info, true) . "</pre>";

// 计算还款信息
$rates = array(3 => 0.025, 6 => 0.023, 12 => 0.02, 18 => 0.018, 24 => 0.016, 36 => 0.015);
$monthly_rate = isset($rates[$user_info['loan_periods']]) ? $rates[$user_info['loan_periods']] : 0.02;
$total_amount = $user_info['loan_amount'] * (1 + $monthly_rate * $user_info['loan_periods']);
$monthly_payment = $user_info['loan_periods'] > 0 ? $total_amount / $user_info['loan_periods'] : 0;
$total_interest = $total_amount - $user_info['loan_amount'];

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 小贷助手 - 用户中心</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; background: #f5f7fa; }
        
        /* APP状态栏 */
        .app-status-bar { background: #667eea; color: white; padding: 10px 20px; display: flex; justify-content: space-between; align-items: center; }
        .back-btn { color: white; text-decoration: none; font-size: 16px; }
        
        /* APP头部 */
        .app-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
        .app-title { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .app-subtitle { font-size: 14px; opacity: 0.9; }
        
        /* 用户信息卡片 */
        .user-card { background: white; margin: 20px; border-radius: 15px; padding: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .user-info { display: flex; align-items: center; margin-bottom: 20px; }
        .user-avatar { width: 60px; height: 60px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; color: white; margin-right: 15px; }
        .user-details h3 { color: #333; margin-bottom: 5px; }
        .user-details p { color: #666; font-size: 14px; }
        
        /* 余额卡片 */
        .balance-card { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .balance-label { font-size: 14px; opacity: 0.9; margin-bottom: 5px; }
        .balance-amount { font-size: 28px; font-weight: bold; }
        .balance-unit { font-size: 16px; margin-left: 5px; }
        
        /* 功能菜单 */
        .menu-section { margin: 20px; }
        .section-title { display: flex; align-items: center; margin-bottom: 15px; font-size: 18px; color: #333; }
        .section-title span { margin-right: 8px; }
        .menu-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; }
        .menu-item { text-decoration: none; }
        .menu-card { background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.2s; }
        .menu-card:hover { transform: translateY(-2px); }
        .menu-icon { font-size: 32px; margin-bottom: 10px; display: block; }
        .menu-text { color: #333; font-weight: 500; margin-bottom: 5px; }
        .menu-amount { color: #28a745; font-weight: bold; font-size: 14px; }
        
        /* 快速操作 */
        .quick-actions { display: flex; gap: 10px; margin-top: 15px; }
        .action-btn { flex: 1; padding: 12px; background: #667eea; color: white; text-decoration: none; border-radius: 8px; text-align: center; font-size: 14px; }
        .action-btn:hover { background: #5a6fd8; }
        
        /* 底部导航 */
        .bottom-nav { position: fixed; bottom: 0; left: 0; right: 0; background: white; border-top: 1px solid #e6e6e6; padding: 10px 0; }
        .nav-items { display: flex; justify-content: space-around; }
        .nav-item { text-decoration: none; color: #666; text-align: center; padding: 5px; }
        .nav-item.active { color: #667eea; }
        .nav-icon { font-size: 20px; display: block; margin-bottom: 2px; }
        .nav-item span:last-child { font-size: 12px; }
        
        /* 统计信息 */
        .stats-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 20px; }
        .stat-card { background: white; padding: 15px; border-radius: 10px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-number { font-size: 20px; font-weight: bold; color: #667eea; margin-bottom: 5px; }
        .stat-label { font-size: 12px; color: #666; }
        
        /* 安全区域 */
        .safe-area { height: 80px; }
    </style>
</head>
<body>
    <!-- APP状态栏 -->
    <div class="app-status-bar">
        <a href="index.php" class="back-btn">← 返回</a>
        <span>💰 小贷助手</span>
        <span></span>
    </div>
    
    <!-- APP头部 -->
    <div class="app-header">
        <div class="app-title">💰 小贷助手</div>
        <div class="app-subtitle">欢迎回来，<?php echo $user_phone; ?></div>
    </div>
    
    <!-- 用户信息卡片 -->
    <div class="user-card">
        <div class="user-info">
            <div class="user-avatar">👤</div>
            <div class="user-details">
                <h3>欢迎回来，<?php echo htmlspecialchars($user_info['customer_name']); ?>！</h3>
                <p><?php echo $user_phone; ?></p>
            </div>
        </div>

        <!-- 借款金额卡片 -->
        <div class="balance-card">
            <div class="balance-label">当前借款金额</div>
            <div class="balance-amount">
                <?php echo number_format($user_info['loan_amount'], 2); ?>
                <span class="balance-unit">元</span>
            </div>
            <div style="font-size: 14px; margin-top: 10px; opacity: 0.9;">
                状态：<?php echo $user_info['loan_status']; ?> |
                分期：<?php echo $user_info['loan_periods']; ?>期
            </div>
        </div>
        
        <!-- 快速操作 - 已移除我的借款、个人中心、退出登录 -->
        <div class="quick-actions" style="display: none;">
            <!-- 功能已移除 -->
        </div>
    </div>
    
    <!-- 借款详细信息 -->
    <div class="user-card" style="margin: 20px;">
        <h3 style="margin-bottom: 20px; color: #333;">📋 我的借款详情</h3>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 5px;">客户姓名</div>
                <div style="font-weight: bold;"><?php echo htmlspecialchars($user_info['customer_name']); ?></div>
            </div>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 5px;">手机号码</div>
                <div style="font-weight: bold;"><?php echo $user_info['phone']; ?></div>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr; gap: 15px; margin-bottom: 20px;">
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 5px;">身份证号</div>
                <div style="font-weight: bold; font-family: monospace;"><?php echo $user_info['id_card']; ?></div>
            </div>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 5px;">银行卡号</div>
                <div style="font-weight: bold; font-family: monospace;"><?php echo $user_info['bank_card']; ?></div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;"><?php echo $user_info['bank_name']; ?></div>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
            <div style="background: #e7f3ff; padding: 15px; border-radius: 8px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 5px;">借款金额</div>
                <div style="font-weight: bold; color: #007bff;">¥<?php echo number_format($user_info['loan_amount'], 2); ?></div>
            </div>
            <div style="background: #e7f3ff; padding: 15px; border-radius: 8px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 5px;">分期数</div>
                <div style="font-weight: bold; color: #007bff;"><?php echo $user_info['loan_periods']; ?>期</div>
            </div>
        </div>

        <div style="background: #f0f8f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <div style="font-size: 12px; color: #666; margin-bottom: 5px;">借款期限</div>
            <?php if (isset($user_info['loan_start_date']) && isset($user_info['loan_end_date']) && $user_info['loan_start_date'] && $user_info['loan_end_date']): ?>
            <div style="font-weight: bold; color: #28a745;">
                <?php echo $user_info['loan_start_date']; ?> 至 <?php echo $user_info['loan_end_date']; ?>
            </div>
            <div style="font-size: 12px; color: #666; margin-top: 5px;">
                <?php
                $start = new DateTime($user_info['loan_start_date']);
                $end = new DateTime($user_info['loan_end_date']);
                $interval = $start->diff($end);
                echo "期限：" . $interval->y . "年" . $interval->m . "个月";
                ?>
            </div>
            <?php else: ?>
            <div style="font-weight: bold; color: #666;">
                <?php echo $user_info['loan_periods']; ?>期 (<?php echo $user_info['loan_periods']; ?>个月)
            </div>
            <div style="font-size: 12px; color: #666; margin-top: 5px;">
                具体期限待确定
            </div>
            <?php endif; ?>
        </div>

        <?php if ($user_info['loan_time']): ?>
        <div style="background: #f0f8f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <div style="font-size: 12px; color: #666; margin-bottom: 5px;">放款时间</div>
            <div style="font-weight: bold; color: #28a745;"><?php echo $user_info['loan_time']; ?></div>
        </div>
        <?php endif; ?>



        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <div style="font-size: 12px; color: #666; margin-bottom: 5px;">借款状态</div>
            <div style="font-weight: bold; color: <?php
                switch($user_info['loan_status']) {
                    case '已放款': echo '#28a745'; break;
                    case '审核中': echo '#ffc107'; break;
                    case '已还款': echo '#17a2b8'; break;
                    case '逾期': echo '#dc3545'; break;
                    default: echo '#6c757d';
                }
            ?>;">● <?php echo $user_info['loan_status']; ?></div>
        </div>

        <!-- 借款合同 -->
        <?php if (isset($user_info['id']) && $user_info['loan_amount'] > 0): ?>
        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin-top: 20px; text-align: center;">
            <div style="font-size: 14px; color: #666; margin-bottom: 10px;">📄 借款合同</div>
            <a href="customer_contract_auto.php?id=<?php echo $user_info['id']; ?>&user=1"
               style="display: inline-block; background: #007bff; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-weight: bold;">
                查看我的借款合同
            </a>
            <div style="font-size: 12px; color: #666; margin-top: 8px;">可查看完整合同内容并打印</div>
        </div>
        <?php endif; ?>
    </div>
    

    
    <!-- 底部安全区域 -->
    <div class="safe-area"></div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="user_index.php?phone=<?php echo urlencode($user_phone); ?>" class="nav-item">
                <span class="nav-icon">🏠</span>
                <span>首页</span>
            </a>
            <a href="user_home.php?phone=<?php echo urlencode($user_phone); ?>" class="nav-item active">
                <span class="nav-icon">💳</span>
                <span>借款信息</span>
            </a>
            <a href="user_profile.php?phone=<?php echo urlencode($user_phone); ?>" class="nav-item">
                <span class="nav-icon">👤</span>
                <span>个人中心</span>
            </a>
            <a href="index.php" class="nav-item">
                <span class="nav-icon">🚪</span>
                <span>退出</span>
            </a>
        </div>
    </div>
</body>
</html>
