@echo off
chcp 65001 >nul
echo ========================================
echo    简化APK编译脚本
echo ========================================
echo.

cd /d "E:\仿随意花小贷源码完整版\android_frontend"

echo [1/3] 检查环境...
if not exist "gradlew.bat" (
    echo ❌ 错误：找不到gradlew.bat文件
    pause
    exit /b 1
)
echo ✅ Gradle环境正常

echo.
echo [2/3] 开始编译APK...
call gradlew.bat assembleDebug
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败
    echo.
    echo 可能的解决方案：
    echo 1. 检查Java/Android SDK环境
    echo 2. 运行 gradlew clean 清理后重试
    echo 3. 检查网络连接（下载依赖）
    pause
    exit /b 1
)

echo ✅ 编译成功

echo.
echo [3/3] 检查生成的APK...
set APK_PATH=app\build\outputs\apk\debug\app-debug.apk
if exist "%APK_PATH%" (
    echo ✅ APK生成成功
    echo 📁 路径：%CD%\%APK_PATH%
    
    for %%A in ("%APK_PATH%") do set APK_SIZE=%%~zA
    echo 📊 大小：%APK_SIZE% 字节
    
    echo.
    echo ========================================
    echo 📱 测试指南：
    echo ========================================
    echo 1. 安装APK到手机：
    echo    adb install -r "%APK_PATH%"
    echo.
    echo 2. 打开APP，在地址栏输入：
    echo    file:///android_asset/debug_test.html
    echo.
    echo 3. 测试权限功能：
    echo    ✓ 检查权限状态
    echo    ✓ 直接权限申请
    echo    ✓ 读取通讯录
    echo.
    echo 4. 同时监控日志：
    echo    adb logcat -s SuiYiHua
    echo ========================================
) else (
    echo ❌ 错误：APK文件未生成
    echo 预期路径：%APK_PATH%
)

echo.
pause
