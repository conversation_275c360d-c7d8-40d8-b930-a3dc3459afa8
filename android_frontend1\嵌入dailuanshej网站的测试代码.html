<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通讯录权限测试 - 随意花小贷</title>
    <style>
        /* 测试面板样式 - 仅在APP内显示 */
        .contact-test-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 15px;
            min-width: 280px;
            max-width: 320px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            color: white;
            font-size: 14px;
            display: none; /* 默认隐藏 */
        }
        
        .test-panel-title {
            font-weight: bold;
            margin-bottom: 12px;
            text-align: center;
            font-size: 16px;
            color: #fff;
        }
        
        .test-button {
            width: 100%;
            margin: 8px 0;
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .test-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }
        
        .test-button:active {
            transform: translateY(0);
        }
        
        .test-result {
            margin-top: 10px;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            word-break: break-all;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }
        
        .status-success { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        
        .toggle-btn {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            display: none; /* 默认隐藏 */
        }
    </style>
</head>
<body>
    <!-- 切换按钮 -->
    <button class="toggle-btn" id="toggleTestPanel" onclick="toggleTestPanel()">📱</button>
    
    <!-- 测试面板 -->
    <div class="contact-test-panel" id="contactTestPanel">
        <div class="test-panel-title">📱 通讯录权限测试</div>
        
        <button class="test-button" onclick="checkContactPermission()">
            <span class="status-indicator status-warning"></span>检查通讯录权限
        </button>
        
        <button class="test-button" onclick="requestContactPermission()">
            <span class="status-indicator status-warning"></span>请求通讯录权限
        </button>
        
        <button class="test-button" onclick="readContacts()">
            <span class="status-indicator status-warning"></span>读取通讯录数据
        </button>
        
        <button class="test-button" onclick="testWebViewInterface()">
            <span class="status-indicator status-warning"></span>测试WebView接口
        </button>
        
        <button class="test-button" onclick="openAppSettings()">
            <span class="status-indicator status-warning"></span>打开应用设置
        </button>
        
        <button class="test-button" onclick="clearResults()">
            <span class="status-indicator"></span>清除结果
        </button>
        
        <div class="test-result" id="testResult">
            <div style="color: #b3d9ff;">等待测试...</div>
        </div>
    </div>

    <script>
        // 检测是否在APP内的WebView环境
        function isInApp() {
            const userAgent = navigator.userAgent.toLowerCase();
            return userAgent.includes('android') && 
                   (userAgent.includes('wv') || window.AndroidContacts !== undefined || 
                    userAgent.includes('suiyihua') || userAgent.includes('loan'));
        }

        // 显示/隐藏测试面板
        function toggleTestPanel() {
            const panel = document.getElementById('contactTestPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 初始化 - 仅在APP内显示
        function initTestPanel() {
            if (isInApp()) {
                document.getElementById('toggleTestPanel').style.display = 'block';
                document.getElementById('contactTestPanel').style.display = 'block';
                logResult('✅ 检测到APP环境，测试面板已启用', 'success');
                
                // 自动检查接口可用性
                setTimeout(() => {
                    testWebViewInterface();
                }, 1000);
            } else {
                logResult('ℹ️ 当前在浏览器环境，测试面板已隐藏', 'warning');
            }
        }

        // 记录测试结果
        function logResult(message, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 'status-warning';
            
            resultDiv.innerHTML += `
                <div style="margin: 5px 0; padding: 4px; border-left: 3px solid ${
                    type === 'success' ? '#4CAF50' : 
                    type === 'error' ? '#f44336' : '#ff9800'
                };">
                    <span class="status-indicator ${statusClass}"></span>
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        // 检查通讯录权限
        function checkContactPermission() {
            logResult('🔍 开始检查通讯录权限状态...', 'info');
            
            if (typeof window.AndroidContacts !== 'undefined') {
                try {
                    const hasPermission = window.AndroidContacts.hasContactPermission();
                    if (hasPermission) {
                        logResult('✅ 通讯录权限已授予', 'success');
                    } else {
                        logResult('❌ 通讯录权限未授予', 'error');
                    }
                    return hasPermission;
                } catch (error) {
                    logResult(`❌ 检查权限时出错: ${error.message}`, 'error');
                    return false;
                }
            } else {
                logResult('❌ AndroidContacts接口不可用', 'error');
                return false;
            }
        }

        // 请求通讯录权限
        function requestContactPermission() {
            logResult('📋 开始请求通讯录权限...', 'info');
            
            if (typeof window.AndroidContacts !== 'undefined') {
                try {
                    window.AndroidContacts.requestContactPermission();
                    logResult('📋 权限请求已发送，请在弹窗中选择"允许"', 'warning');
                    
                    // 延迟检查权限结果
                    setTimeout(() => {
                        const granted = checkContactPermission();
                        if (granted) {
                            logResult('🎉 权限请求成功！', 'success');
                        } else {
                            logResult('⚠️ 权限可能被拒绝，请检查应用设置', 'warning');
                        }
                    }, 2000);
                } catch (error) {
                    logResult(`❌ 请求权限时出错: ${error.message}`, 'error');
                }
            } else {
                logResult('❌ AndroidContacts接口不可用', 'error');
            }
        }

        // 读取通讯录数据
        function readContacts() {
            logResult('📖 开始读取通讯录数据...', 'info');
            
            if (typeof window.AndroidContacts !== 'undefined') {
                try {
                    // 先检查权限
                    if (!window.AndroidContacts.hasContactPermission()) {
                        logResult('❌ 没有通讯录权限，请先授权', 'error');
                        return;
                    }
                    
                    const contacts = window.AndroidContacts.getContacts();
                    if (contacts && contacts.length > 0) {
                        logResult(`✅ 成功读取 ${contacts.length} 条通讯录记录`, 'success');
                        
                        // 显示前3条联系人作为示例
                        const preview = contacts.slice(0, 3).map(contact => 
                            `${contact.name}: ${contact.phone}`
                        ).join(', ');
                        logResult(`📞 示例联系人: ${preview}${contacts.length > 3 ? '...' : ''}`, 'info');
                        
                        // 返回数据给页面其他功能使用
                        if (window.contactsCallback) {
                            window.contactsCallback(contacts);
                        }
                    } else {
                        logResult('⚠️ 通讯录为空或读取失败', 'warning');
                    }
                } catch (error) {
                    logResult(`❌ 读取通讯录时出错: ${error.message}`, 'error');
                }
            } else {
                logResult('❌ AndroidContacts接口不可用', 'error');
            }
        }

        // 测试WebView接口
        function testWebViewInterface() {
            logResult('🔧 测试WebView接口可用性...', 'info');
            
            // 检查各种可能的接口
            const interfaces = [
                'AndroidContacts',
                'Android',
                'WebViewInterface',
                'NativeInterface'
            ];
            
            let foundInterfaces = [];
            interfaces.forEach(interfaceName => {
                if (typeof window[interfaceName] !== 'undefined') {
                    foundInterfaces.push(interfaceName);
                }
            });
            
            if (foundInterfaces.length > 0) {
                logResult(`✅ 发现可用接口: ${foundInterfaces.join(', ')}`, 'success');
                
                // 测试AndroidContacts的具体方法
                if (typeof window.AndroidContacts !== 'undefined') {
                    const methods = ['hasContactPermission', 'requestContactPermission', 'getContacts'];
                    let availableMethods = [];
                    
                    methods.forEach(method => {
                        if (typeof window.AndroidContacts[method] === 'function') {
                            availableMethods.push(method);
                        }
                    });
                    
                    logResult(`📋 AndroidContacts可用方法: ${availableMethods.join(', ')}`, 'info');
                }
            } else {
                logResult('❌ 未发现任何WebView接口', 'error');
                logResult('💡 请确保APP正确注入了JavaScript接口', 'warning');
            }
        }

        // 打开应用设置
        function openAppSettings() {
            logResult('⚙️ 尝试打开应用设置...', 'info');
            
            if (typeof window.AndroidContacts !== 'undefined' && 
                typeof window.AndroidContacts.openAppSettings === 'function') {
                try {
                    window.AndroidContacts.openAppSettings();
                    logResult('✅ 应用设置页面已打开', 'success');
                } catch (error) {
                    logResult(`❌ 打开设置时出错: ${error.message}`, 'error');
                }
            } else {
                logResult('❌ openAppSettings方法不可用', 'error');
                logResult('💡 请手动进入：设置 > 应用管理 > 随意花 > 权限', 'warning');
            }
        }

        // 清除结果
        function clearResults() {
            document.getElementById('testResult').innerHTML = 
                '<div style="color: #b3d9ff;">结果已清除...</div>';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTestPanel();
        });

        // 为外部调用提供全局函数
        window.contactsTestPanel = {
            checkPermission: checkContactPermission,
            requestPermission: requestContactPermission,
            readContacts: readContacts,
            testInterface: testWebViewInterface,
            openSettings: openAppSettings,
            logResult: logResult
        };

        // 监听页面可见性变化，当从设置返回时重新检查权限
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && isInApp()) {
                setTimeout(() => {
                    logResult('👀 页面重新激活，检查权限状态...', 'info');
                    checkContactPermission();
                }, 500);
            }
        });
    </script>
</body>
</html>
