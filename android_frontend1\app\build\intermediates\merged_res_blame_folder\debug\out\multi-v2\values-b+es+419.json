{"logs": [{"outputFile": "com.dailuanshej.loan.app-mergeDebugResources-27:/values-b+es+419/values-b+es+419.xml", "map": [{"source": "E:\\SDK\\caches\\transforms-3\\7cfde09bef56db81a739e41054cc6284\\transformed\\material-1.7.0\\res\\values-b+es+419\\values-b+es+419.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,364,466,594,675,740,835,905,968,1061,1133,1196,1270,1334,1390,1508,1566,1628,1684,1764,1898,1987,2068,2209,2290,2370,2460,2516,2572,2638,2714,2796,2884,2957,3034,3104,3181,3270,3344,3438,3540,3612,3693,3797,3850,3917,4010,4099,4161,4225,4288,4399,4496,4598,4696,4756,4816", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,89,55,55,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,59,59,82", "endOffsets": "273,359,461,589,670,735,830,900,963,1056,1128,1191,1265,1329,1385,1503,1561,1623,1679,1759,1893,1982,2063,2204,2285,2365,2455,2511,2567,2633,2709,2791,2879,2952,3029,3099,3176,3265,3339,3433,3535,3607,3688,3792,3845,3912,4005,4094,4156,4220,4283,4394,4491,4593,4691,4751,4811,4894"}}]}]}