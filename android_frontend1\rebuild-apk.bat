@echo off
echo ========================================
echo 🔄 重新编译APK - 修复签字区域布局
echo ========================================

echo.
echo 📱 步骤1: 清理旧的构建文件...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 已清理 app\build 目录
)

if exist "build" (
    rmdir /s /q "build"
    echo ✅ 已清理 build 目录
)

echo.
echo 🔧 步骤2: 开始构建APK...
call gradlew clean
if %errorlevel% neq 0 (
    echo ❌ Clean 失败
    pause
    exit /b 1
)

call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo 📦 步骤3: 复制APK文件...
set APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk
set APK_DEST=youyihua_v1.0.2_fixed.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%"
    echo ✅ APK已复制到: %APK_DEST%
) else (
    echo ❌ 找不到构建的APK文件: %APK_SOURCE%
    pause
    exit /b 1
)

echo.
echo 📤 步骤4: 复制到服务器目录...
if exist "..\源码\apk\" (
    copy "%APK_DEST%" "..\源码\apk\youyihua_v1.0.2_fixed.apk"
    echo ✅ APK已复制到服务器目录
) else (
    echo ⚠️ 服务器目录不存在，请手动复制APK文件
)

echo.
echo ========================================
echo ✅ APK重新编译完成！
echo ========================================
echo.
echo 📋 修复内容：
echo   • 使用表格布局替代Flexbox
echo   • 固定SVG印章尺寸
echo   • 添加APK WebView兼容样式
echo   • 确保甲乙方签字区域正确排列
echo   • 创建APK专用合同页面
echo   • 支持上传印章图片显示
echo   • 修复APK打印功能
echo   • 添加合同分享功能
echo   • 🆕 修复APK横向容器问题
echo   • 🆕 防止横向滚动和溢出
echo   • 🆕 强制文字水平显示
echo   • 🆕 响应式表格布局
echo.
echo 📱 测试步骤：
echo   1. 安装新的APK: youyihua_v1.0.2_fixed.apk
echo   2. 登录并查看合同页面
echo   3. 检查甲方签字是否在左侧
echo   4. 检查乙方印章是否在右侧
echo   5. 确认印章显示正常
echo   6. 🆕 确认无横向滚动
echo   7. 🆕 确认文字水平显示
echo   8. 🆕 测试打印功能
echo   9. 🆕 测试分享功能
echo.
echo 🔍 横向容器测试：
echo   访问 https://dailuanshej.cn/apk_horizontal_test.php
echo.
pause
