@echo off
chcp 65001 >nul
echo ========================================
echo      Copy Project to English Path
echo ========================================
echo.

set "SOURCE_PATH=E:\仿随意花小贷源码完整版\android_frontend"
set "TARGET_PATH=E:\android_frontend"

echo Source: %SOURCE_PATH%
echo Target: %TARGET_PATH%
echo.

:: Check if source exists
if not exist "%SOURCE_PATH%" (
    echo ❌ Source path not found: %SOURCE_PATH%
    pause
    exit /b 1
)

:: Create target directory if it doesn't exist
if not exist "%TARGET_PATH%" (
    echo Creating target directory...
    mkdir "%TARGET_PATH%"
    if %ERRORLEVEL% neq 0 (
        echo ❌ Failed to create target directory
        pause
        exit /b 1
    )
    echo ✅ Created: %TARGET_PATH%
) else (
    echo Target directory already exists
    echo.
    echo Do you want to update the existing files? (y/n)
    set /p update="Update files? (y/n): "
    if /i not "%update%"=="y" (
        echo Skipping copy, using existing files
        goto :build_apk
    )
)

echo.
echo Copying project files...
echo This may take a moment...

:: Copy all files and folders
xcopy "%SOURCE_PATH%\*" "%TARGET_PATH%\" /E /I /H /Y >nul 2>&1

if %ERRORLEVEL% equ 0 (
    echo ✅ Project copied successfully
) else (
    echo ❌ Copy failed with error code: %ERRORLEVEL%
    pause
    exit /b 1
)

:build_apk
echo.
echo ========================================
echo           Building APK
echo ========================================
echo.

:: Change to target directory
cd /d "%TARGET_PATH%"

if %ERRORLEVEL% neq 0 (
    echo ❌ Cannot access target directory
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

:: Check if gradlew.bat exists
if not exist "gradlew.bat" (
    echo ❌ gradlew.bat not found in target directory
    echo Please check if the copy was successful
    pause
    exit /b 1
)

:: Clean old build
echo Cleaning old build files...
if exist "app\build\outputs\apk" (
    rmdir /s /q "app\build\outputs\apk" 2>nul
    echo Old APK files cleaned
)

echo.
echo Starting Gradle build...
echo This may take several minutes...
echo.

:: Build APK
gradlew.bat clean assembleDebug --stacktrace

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo           BUILD SUCCESSFUL!
    echo ========================================
    echo.
    
    :: Find and copy APK
    echo Looking for generated APK...
    for /r "app\build\outputs\apk" %%f in (*.apk) do (
        echo.
        echo ✅ Found APK: %%f
        
        :: Get file size
        for %%a in ("%%f") do set "size=%%~za"
        echo File size: !size! bytes
        
        :: Copy to desktop and target root
        set "desktop=%USERPROFILE%\Desktop"
        copy "%%f" "!desktop!\SuiYiHua-New.apk" >nul 2>&1
        copy "%%f" "%TARGET_PATH%\SuiYiHua-New.apk" >nul 2>&1
        
        if exist "!desktop!\SuiYiHua-New.apk" (
            echo ✅ APK copied to desktop: SuiYiHua-New.apk
        )
        
        if exist "%TARGET_PATH%\SuiYiHua-New.apk" (
            echo ✅ APK copied to project root: %TARGET_PATH%\SuiYiHua-New.apk
        )
        
        echo.
        echo 📱 Next steps:
        echo 1. Transfer SuiYiHua-New.apk to your phone
        echo 2. Install the APK (enable unknown sources if needed)
        echo 3. Test contacts functionality
        echo 4. Use adb logcat -s SuiYiHua to view logs
        echo.
        echo 🔧 Project location: %TARGET_PATH%
        echo 📱 APK location: !desktop!\SuiYiHua-New.apk
        
        goto :build_success
    )
    
    echo ❌ No APK files found in build output
    echo Please check the build logs above
    
) else (
    echo.
    echo ========================================
    echo            BUILD FAILED!
    echo ========================================
    echo.
    echo Error code: %ERRORLEVEL%
    echo.
    echo 💡 Common solutions:
    echo 1. Check Java/JDK installation
    echo 2. Run fix_java.bat to configure JDK
    echo 3. Check internet connection
    echo 4. Try: gradlew.bat clean
    echo.
    echo Project is now at: %TARGET_PATH%
    echo You can open this in Android Studio for debugging
)

:build_success
echo.
echo ========================================
echo Press any key to exit...
pause >nul
