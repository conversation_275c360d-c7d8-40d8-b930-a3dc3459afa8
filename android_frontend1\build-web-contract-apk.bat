@echo off
echo ========================================
echo 📱 构建Web合同APK - 完全使用Web端
echo ========================================

echo.
echo 📋 构建特性：
echo   • APK直接使用Web端合同页面
echo   • 完全保持Web端的样式和功能
echo   • 无任何特殊处理或重定向
echo   • 与Web端完全一致的用户体验
echo.

echo 🔧 步骤1: 清理旧的构建文件...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 已清理 app\build 目录
)

if exist "build" (
    rmdir /s /q "build"
    echo ✅ 已清理 build 目录
)

echo.
echo 🔧 步骤2: 开始构建APK...
call gradlew clean
if %errorlevel% neq 0 (
    echo ❌ Clean 失败
    pause
    exit /b 1
)

call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo 📦 步骤3: 复制APK文件...
set APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk
set APK_DEST=youyihua_web_contract.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%"
    echo ✅ APK已复制到: %APK_DEST%
) else (
    echo ❌ 找不到构建的APK文件: %APK_SOURCE%
    pause
    exit /b 1
)

echo.
echo 📤 步骤4: 复制到服务器目录...
if exist "..\源码\apk\" (
    copy "%APK_DEST%" "..\源码\apk\%APK_DEST%"
    echo ✅ APK已复制到服务器目录
) else (
    echo ⚠️ 服务器目录不存在，请手动复制APK文件
)

echo.
echo ========================================
echo ✅ Web合同APK构建完成！
echo ========================================
echo.
echo 📱 APK特性：
echo   ✅ 直接访问：https://dailuanshej.cn/customer_contract_auto.php?id=2&user=1
echo   ✅ 完全保持Web端样式和版面
echo   ✅ 支持所有Web端功能
echo   ✅ 印章正常显示
echo   ✅ 打印功能正常
echo   ✅ 无任何重定向或特殊处理
echo.
echo 🧪 测试步骤：
echo   1. 安装APK: %APK_DEST%
echo   2. 打开应用并登录
echo   3. 点击查看合同
echo   4. 验证合同页面与Web端完全一致
echo   5. 测试印章显示
echo   6. 测试打印功能
echo.
echo 🔍 如果遇到问题：
echo   1. 检查网络连接
echo   2. 在手机浏览器中测试相同URL
echo   3. 检查防火墙设置
echo   4. 查看APK日志输出
echo.
echo 💡 调试方法：
echo   • 访问调试页面：https://dailuanshej.cn/apk_debug.php
echo   • 使用Chrome inspect查看WebView日志
echo   • 检查网络请求状态
echo.
pause
