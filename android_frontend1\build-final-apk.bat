@echo off
echo ========================================
echo 🎯 构建最终版APK - 正常启动首页
echo ========================================

echo.
echo 📋 最终版特性：
echo   ✅ 启动时加载正常首页 (https://dailuanshej.cn)
echo   ✅ 合同页面正常显示
echo   ✅ 保留详细错误诊断功能
echo   ✅ 移除测试页面干扰
echo   ✅ 完整的Web端功能
echo.

echo 🔧 步骤1: 清理构建环境...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 已清理app构建缓存
)

if exist "build" (
    rmdir /s /q "build"
    echo ✅ 已清理项目缓存
)

echo.
echo 🔧 步骤2: 验证配置...
echo 检查WEBSITE_URL配置...

findstr /C:"https://dailuanshej.cn" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ 网站URL配置正确
) else (
    echo ❌ 网站URL配置错误
    pause
    exit /b 1
)

echo.
echo 🔧 步骤3: 构建最终版APK...

call .\gradlew clean
if %errorlevel% neq 0 (
    echo ⚠️ Clean有警告，继续构建...
)

echo 正在构建APK，请稍候...
call .\gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    echo.
    echo 💡 请检查：
    echo   1. Java环境是否正确配置
    echo   2. Android SDK是否可用
    echo   3. 网络连接是否正常
    echo.
    pause
    exit /b 1
)

echo.
echo 📦 步骤4: 复制最终版APK...
set APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk
set APK_DEST=youyihua_final.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%"
    echo ✅ 最终版APK已生成: %APK_DEST%
    
    REM 显示文件信息
    for %%A in ("%APK_DEST%") do (
        set APK_SIZE=%%~zA
        set APK_DATE=%%~tA
    )
    echo   文件大小: %APK_SIZE% 字节
    echo   创建时间: %APK_DATE%
    
) else (
    echo ❌ 找不到构建的APK文件
    pause
    exit /b 1
)

echo.
echo 📤 步骤5: 部署到服务器目录...
if exist "..\源码\apk\" (
    copy "%APK_DEST%" "..\源码\apk\%APK_DEST%"
    echo ✅ APK已部署到服务器目录
) else (
    echo ⚠️ 服务器目录不存在
)

echo.
echo 🧹 步骤6: 清理临时文件...
if exist "youyihua_debug.apk" (
    del "youyihua_debug.apk"
    echo ✅ 已删除调试版APK
)

if exist "youyihua_fixed.apk" (
    del "youyihua_fixed.apk"
    echo ✅ 已删除修复版APK
)

echo.
echo ========================================
echo 🎉 最终版APK构建完成！
echo ========================================
echo.
echo 📱 APK功能特性：
echo   🏠 启动页面: https://dailuanshej.cn (正常首页)
echo   📄 合同功能: 完全支持Web端合同显示
echo   🖨️ 打印功能: 支持合同打印
echo   🔒 印章显示: 支持合同印章
echo   📱 移动优化: 完整的移动端适配
echo   🔍 错误诊断: 详细的错误信息显示
echo.
echo 🧪 使用说明：
echo   1. 卸载之前的测试版本
echo   2. 安装最终版: %APK_DEST%
echo   3. 打开应用 → 自动加载首页
echo   4. 手机号登录 → 查看合同
echo   5. 合同页面应该正常显示
echo   6. 支持打印和所有Web端功能
echo.
echo 🎯 测试重点：
echo   ✅ 启动不再显示测试页面
echo   ✅ 直接进入正常的登录首页
echo   ✅ 合同页面完全正常
echo   ✅ 印章和打印功能正常
echo.
echo 💡 如果遇到问题：
echo   • 检查网络连接
echo   • 确认服务器正常运行
echo   • 查看APK错误提示
echo   • 对比手机浏览器访问效果
echo.
pause
