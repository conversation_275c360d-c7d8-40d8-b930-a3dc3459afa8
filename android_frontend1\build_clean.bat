@echo off
echo ========================================
echo Build APK for SuiYiHua App
echo SDK Path: E:\SDK
echo ========================================
echo.

echo Setting up Java environment...

REM Check common JDK locations
set JAVA_FOUND=0

echo Checking E:\SDK for JDK...

if exist "E:\SDK\jdk\bin\java.exe" (
    set "JAVA_HOME=E:\SDK\jdk"
    set JAVA_FOUND=1
    echo Found Java: %JAVA_HOME%
    goto setup_env
)

REM Check for versioned JDK directories
for /d %%i in ("E:\SDK\jdk*") do (
    if exist "%%i\bin\java.exe" (
        set "JAVA_HOME=%%i"
        set JAVA_FOUND=1
        echo Found Java: %%i
        goto setup_env
    )
)

REM Check Android Studio JDK as fallback
if exist "C:\Program Files\Android\Android Studio\jbr\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Android\Android Studio\jbr"
    set JAVA_FOUND=1
    echo Found Android Studio JDK: %JAVA_HOME%
    goto setup_env
)

if exist "C:\Program Files (x86)\Android\Android Studio\jbr\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files (x86)\Android\Android Studio\jbr"
    set JAVA_FOUND=1
    echo Found Android Studio JDK: %JAVA_HOME%
    goto setup_env
)

REM Manual input if not found
echo Auto search failed. Please check these possible JDK locations:
echo 1. E:\SDK\jdk
echo 2. E:\SDK\jdk-11 (or other version)
echo 3. Android Studio install directory\jbr
echo.
set /p JAVA_HOME="Enter complete JDK path: "

if exist "%JAVA_HOME%\bin\java.exe" (
    set JAVA_FOUND=1
    echo Java path confirmed
) else (
    echo Invalid Java path: %JAVA_HOME%
    pause
    exit /b 1
)

:setup_env
echo.
echo Setting environment variables...
set "PATH=%JAVA_HOME%\bin;%PATH%"
set "ANDROID_HOME=E:\SDK"
set "ANDROID_SDK_ROOT=E:\SDK"

echo Current environment:
echo JAVA_HOME = %JAVA_HOME%
echo ANDROID_HOME = %ANDROID_HOME%
echo.

echo Testing Java version...
java -version
if %errorlevel% neq 0 (
    echo Java cannot run
    pause
    exit /b 1
)

echo.
echo ========================================
echo Building SuiYiHua APK
echo ========================================

echo Step 1: Clean cache
if exist ".gradle" rmdir /s /q ".gradle"
if exist "app\build" rmdir /s /q "app\build"
echo Cache cleaned

echo.
echo Step 2: Gradle clean
gradlew.bat clean
if %errorlevel% neq 0 (
    echo Gradle clean failed
    pause
    exit /b 1
)

echo.
echo Step 3: Build debug APK
echo Please wait, first build may take 5-10 minutes...
gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo APK build failed
    echo.
    echo Possible solutions:
    echo 1. Check network connection
    echo 2. Confirm JDK version compatibility
    echo 3. Clean and retry
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build Success!
echo ========================================

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo APK file found
    
    copy "app\build\outputs\apk\debug\app-debug.apk" "SuiYiHua-Loan.apk" >nul
    echo APK copied as: SuiYiHua-Loan.apk
    
    for %%A in ("SuiYiHua-Loan.apk") do (
        set /a size=%%~zA/1024/1024
        echo APK size: !size! MB
    )
    
    echo.
    echo APK locations:
    echo - Original: app\build\outputs\apk\debug\app-debug.apk
    echo - Copy: %CD%\SuiYiHua-Loan.apk
    
) else (
    echo APK file not found
    echo Check: app\build\outputs\apk\debug\ directory
)

echo.
echo ========================================
echo App Information
echo ========================================
echo App Name: SuiYiHua Loan Assistant
echo Package: com.dailuanshej.loan
echo Min Android: 7.0 (API 24)
echo Target Android: 14 (API 34)
echo Features: WebView + Native Contacts

echo.
echo Installation Instructions:
echo 1. Transfer APK to Android device
echo 2. Enable "Unknown sources" in device settings
echo 3. Tap APK file to install
echo 4. Grant contacts permission when prompted
echo 5. App will load: https://dailuanshej.cn

echo.
pause
