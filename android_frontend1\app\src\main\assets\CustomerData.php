<?php
/**
 * 客户数据管理类
 * 统一管理客户数据的读取和保存
 */
class CustomerData {
    private static $dataFile = 'customers.json';
    
    /**
     * 获取所有客户数据
     */
    public static function getAllCustomers() {
        if (!file_exists(self::$dataFile)) {
            return [];
        }
        
        $jsonData = file_get_contents(self::$dataFile);
        $data = json_decode($jsonData, true);
        
        return $data ? $data : [];
    }
    
    /**
     * 根据ID获取客户数据
     */
    public static function getCustomerById($id) {
        $customers = self::getAllCustomers();
        
        foreach ($customers as $customer) {
            if ($customer['id'] == $id) {
                return $customer;
            }
        }
        
        return null;
    }
    
    /**
     * 保存客户数据
     */
    public static function saveCustomer($customerData) {
        $customers = self::getAllCustomers();
        $found = false;
        
        // 更新现有客户
        for ($i = 0; $i < count($customers); $i++) {
            if ($customers[$i]['id'] == $customerData['id']) {
                $customers[$i] = $customerData;
                $found = true;
                break;
            }
        }
        
        // 添加新客户
        if (!$found) {
            $customers[] = $customerData;
        }
        
        return self::saveAllCustomers($customers);
    }
    
    /**
     * 保存所有客户数据
     */
    public static function saveAllCustomers($customers) {
        $jsonData = json_encode($customers, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents(self::$dataFile, $jsonData) !== false;
    }
    
    /**
     * 删除客户
     */
    public static function deleteCustomer($id) {
        $customers = self::getAllCustomers();

        for ($i = 0; $i < count($customers); $i++) {
            if ($customers[$i]['id'] == $id) {
                array_splice($customers, $i, 1);
                return self::saveAllCustomers($customers);
            }
        }

        return false;
    }

    /**
     * 更新客户数据（兼容方法）
     */
    public static function updateCustomer($id, $data) {
        $data['id'] = $id;
        return self::saveCustomer($data);
    }

    /**
     * 获取客户（兼容方法）
     */
    public static function getCustomer($id) {
        return self::getCustomerById($id);
    }
}
?>
