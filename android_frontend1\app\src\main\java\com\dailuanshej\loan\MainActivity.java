package com.dailuanshej.loan;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 主Activity - 简化版本，已移除所有权限相关功能
 * 仅保留基本的WebView功能和APK专用接口
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final String WEBSITE_URL = "https://dailuanshej.cn/";

    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Log.d(TAG, "=== MainActivity启动 ===");

        initWebView();
        loadWebsite();

        Log.d(TAG, "=== MainActivity初始化完成 ===");
    }

    /**
     * 初始化WebView
     */
    private void initWebView() {
        webView = findViewById(R.id.webview);

        // WebView基本设置
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        webSettings.setUserAgentString(webSettings.getUserAgentString() + " DaiLuanSheApp/1.0");

        // 添加JavaScript接口
        webView.addJavascriptInterface(new ApkInterface(this), "ApkInterface");

        // 设置WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "页面加载完成: " + url);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Log.e(TAG, "页面加载错误: " + description);
            }
        });

        Log.d(TAG, "WebView初始化完成");
    }

    /**
     * 加载网站
     */
    private void loadWebsite() {
        Log.d(TAG, "开始加载网站: " + WEBSITE_URL);
        webView.loadUrl(WEBSITE_URL);
    }

    private String getWebViewVersion() {
        try {
            android.content.pm.PackageInfo info = getPackageManager().getPackageInfo("com.google.android.webview", 0);
            return info.versionName;
        } catch (android.content.pm.PackageManager.NameNotFoundException e) {
            return "N/A";
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    /**
     * APK专用功能接口 - 支持合同打印和分享
     */
    public class ApkInterface {
        private Context context;

        public ApkInterface(Context context) {
            this.context = context;
        }

        @JavascriptInterface
        public void shareContract(String title, String url) {
            Log.d(TAG, "分享合同: " + title + " - " + url);

            try {
                Intent shareIntent = new Intent(Intent.ACTION_SEND);
                shareIntent.setType("text/plain");
                shareIntent.putExtra(Intent.EXTRA_SUBJECT, title);
                shareIntent.putExtra(Intent.EXTRA_TEXT, "合同链接：" + url);

                Intent chooser = Intent.createChooser(shareIntent, "分享合同");
                if (shareIntent.resolveActivity(context.getPackageManager()) != null) {
                    context.startActivity(chooser);
                } else {
                    Log.e(TAG, "没有找到可用的分享应用");
                }
            } catch (Exception e) {
                Log.e(TAG, "分享合同失败: " + e.getMessage());
            }
        }

        @JavascriptInterface
        public void printContract(String contractHtml) {
            Log.d(TAG, "打印合同请求");

            runOnUiThread(() -> {
                Toast.makeText(context, "打印功能开发中...", Toast.LENGTH_SHORT).show();
            });
        }

        @JavascriptInterface
        public String getAppInfo() {
            try {
                org.json.JSONObject info = new org.json.JSONObject();
                info.put("appName", "优易花");
                info.put("appVersion", "1.0.1");
                info.put("platform", "Android");
                info.put("webViewVersion", getWebViewVersion());
                info.put("androidVersion", android.os.Build.VERSION.RELEASE);
                info.put("sdkVersion", android.os.Build.VERSION.SDK_INT);
                info.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date()));

                return info.toString();
            } catch (Exception e) {
                Log.e(TAG, "获取应用信息失败", e);
                return "{}";
            }
        }

        @JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(() -> {
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
            });
        }

        @JavascriptInterface
        public void logMessage(String message) {
            Log.d(TAG, "JS日志: " + message);
        }
    }
}

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

// APK专用功能导入
import android.print.PrintManager;
import android.print.PrintDocumentAdapter;
import android.print.PrintAttributes;
import android.app.AlertDialog;
import android.content.DialogInterface;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "YouYiHua";
    private WebView webView;

    // 关键改动：URL已改回线上正式地址
    // private static final String WEBSITE_URL = "file:///android_asset/final_test.html";
    private static final String WEBSITE_URL = "https://dailuanshej.cn";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 应用安全检查 - 防止被识别为恶意软件
        performSecurityChecks();

        initWebView();
        loadWebsite();
    }

    /**
     * 执行应用安全检查
     * 向系统证明应用的合法性
     */
    private void performSecurityChecks() {
        try {
            Log.d(TAG, "🔒 开始应用安全检查");

            // 记录应用身份信息
            SecurityHelper.logAppIdentity(this);

            // 验证应用签名
            boolean signatureValid = SecurityHelper.verifyAppSignature(this);
            if (!signatureValid) {
                Log.w(TAG, "⚠️ 应用签名验证失败");
            }

            // 检查设备安全状态
            boolean deviceSecure = SecurityHelper.isDeviceSecure(this);
            if (!deviceSecure) {
                Log.w(TAG, "⚠️ 设备安全检查失败");
            }

            // 检查安装来源
            String installSource = SecurityHelper.getInstallSource(this);
            Log.d(TAG, "📱 安装来源: " + installSource);

            Log.d(TAG, "✅ 应用安全检查完成");

        } catch (Exception e) {
            Log.e(TAG, "安全检查异常: " + e.getMessage());
        }
    }

    private void initWebView() {
        webView = findViewById(R.id.webView);
        WebSettings webSettings = webView.getSettings();
        
        // 基础JavaScript设置
        webSettings.setJavaScriptEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        
        // 存储设置
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);
        // setAppCacheEnabled 已弃用，移除此行
        
        // 文件访问设置
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        
        // 以下方法需要API 16+
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN) {
            webSettings.setAllowFileAccessFromFileURLs(true);
            webSettings.setAllowUniversalAccessFromFileURLs(true);
        }
        
        // 网络和安全设置
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        webSettings.setBlockNetworkImage(false);
        webSettings.setBlockNetworkLoads(false);
        
        // 启用 DOM Storage
        webSettings.setDomStorageEnabled(true);
        
        // 缓存设置
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // 用户代理设置，确保网站识别为移动端
        String userAgent = webSettings.getUserAgentString();
        webSettings.setUserAgentString(userAgent + " YouYiHuaApp/1.0");
        
        // 其他重要设置
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setDisplayZoomControls(false);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        
        // 启用调试（仅在Debug模式下）
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        
        // 同时注册两个接口名，确保兼容性
        ContactsJSInterface jsInterface = new ContactsJSInterface();
        webView.addJavascriptInterface(jsInterface, "AndroidContacts");
        webView.addJavascriptInterface(jsInterface, "AndroidInterface");

        // 注册APK专用功能接口
        webView.addJavascriptInterface(new ApkInterface(this), "Android");

        Log.d(TAG, "✅ JavaScript接口已注册：AndroidContacts、AndroidInterface 和 Android");
        
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                Log.d(TAG, "URL加载请求: " + url);

                // APK完全按照Web端方式工作，不做任何特殊处理
                // 直接加载Web端的合同页面，保持原有的样式和功能
                return false; // 返回false让WebView自己处理URL加载
            }
            
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                Log.e(TAG, "=== 页面加载错误详情 ===");
                Log.e(TAG, "错误代码: " + errorCode);
                Log.e(TAG, "错误描述: " + description);
                Log.e(TAG, "失败URL: " + failingUrl);
                Log.e(TAG, "========================");

                // 显示详细的错误信息给用户
                String errorHtml = "<html><body style='font-family: Arial; padding: 20px; text-align: center;'>" +
                        "<h2 style='color: red;'>❌ 网络错误</h2>" +
                        "<p><strong>错误代码:</strong> " + errorCode + "</p>" +
                        "<p><strong>错误描述:</strong> " + description + "</p>" +
                        "<p><strong>失败URL:</strong> " + failingUrl + "</p>" +
                        "<hr>" +
                        "<p>请检查:</p>" +
                        "<ul style='text-align: left; max-width: 300px; margin: 0 auto;'>" +
                        "<li>网络连接是否正常</li>" +
                        "<li>服务器是否可访问</li>" +
                        "<li>URL是否正确</li>" +
                        "</ul>" +
                        "<button onclick='window.location.reload()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; margin-top: 20px;'>重试</button>" +
                        "</body></html>";

                view.loadDataWithBaseURL(null, errorHtml, "text/html", "UTF-8", null);
            }

            @Override
            public void onReceivedHttpError(WebView view, android.webkit.WebResourceRequest request, android.webkit.WebResourceResponse errorResponse) {
                String url = request.getUrl().toString();
                Log.e(TAG, "HTTP错误: " + errorResponse.getStatusCode() + " URL: " + url);

                // 记录错误但不自动跳转，让用户看到实际的错误信息
                super.onReceivedHttpError(view, request, errorResponse);
            }

            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d(TAG, "页面开始加载: " + url);
            }
            
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "页面加载完成: " + url);
                
                // 延迟注入，确保页面DOM完全加载
                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                    injectContactsScript();
                    // 注入移动端文字横排修复
                    injectMobileTextFix();
                    // 已移除调试按钮的注入
                }, 1000);
            }
        });
        
        // 设置WebChromeClient以支持更多功能
        webView.setWebChromeClient(new android.webkit.WebChromeClient() {
            @Override
            public boolean onConsoleMessage(android.webkit.ConsoleMessage consoleMessage) {
                Log.d(TAG, "WebView控制台: " + consoleMessage.message() + 
                      " -- 来源: " + consoleMessage.sourceId() + ":" + consoleMessage.lineNumber());
                return true;
            }
            
            @Override
            public boolean onJsAlert(WebView view, String url, String message, android.webkit.JsResult result) {
                Log.d(TAG, "JS Alert: " + message);
                return super.onJsAlert(view, url, message, result);
            }
        });
    }

    private void loadWebsite() {
        // 确保加载正常的首页，不是测试页面
        String homeUrl = "https://dailuanshej.cn";
        Log.d(TAG, "=== 加载正常首页 ===");
        Log.d(TAG, "首页URL: " + homeUrl);
        Log.d(TAG, "不加载任何测试页面");
        Log.d(TAG, "==================");

        webView.loadUrl(homeUrl);
    }



    // 移动端文字横排修复 - 注入CSS样式
    private void injectMobileTextFix() {
        Log.d(TAG, "=== 注入移动端文字横排修复样式 ===");
        String fixScript =
            "javascript:" +
            "(function(){" +
            "console.log('开始应用移动端文字横排修复');" +
            "var style = document.createElement('style');" +
            "style.innerHTML = '" +
            "* { writing-mode: horizontal-tb !important; text-orientation: mixed !important; direction: ltr !important; } " +
            ".container { width: 100% !important; max-width: 100% !important; margin: 0 !important; padding: 15px !important; box-sizing: border-box !important; } " +
            "table, td, th, .label, .info-table { writing-mode: horizontal-tb !important; text-orientation: mixed !important; } " +
            ".info-table td { word-wrap: break-word !important; word-break: break-all !important; } " +

            // APK印章显示优化
            "#lender-seal-area { min-height: 80px !important; height: auto !important; position: relative !important; } " +
            "#lender-seal-img { max-width: 120px !important; max-height: 120px !important; width: auto !important; height: auto !important; position: absolute !important; right: 180px !important; top: -20px !important; z-index: 10 !important; } " +
            ".signature-area { min-height: 150px !important; } " +
            ".signature-box { min-height: 120px !important; position: relative !important; } " +

            // 移动端特殊优化
            "@media (max-width: 768px) { " +
            ".container { padding: 10px !important; } " +
            "#lender-seal-img { max-width: 100px !important; max-height: 100px !important; right: 150px !important; top: -15px !important; } " +
            ".signature-area { min-height: 130px !important; } " +
            "} " +
            "';" +
            "document.head.appendChild(style);" +
            "console.log('移动端文字横排修复已应用');" +
            "})()";

        webView.evaluateJavascript(fixScript, new ValueCallback<String>() {
            @Override
            public void onReceiveValue(String value) {
                Log.d(TAG, "文字横排修复脚本执行完成: " + value);
            }
        });
    }

    private void injectContactsScript() {
        Log.d(TAG, "=== 开始注入通讯录JavaScript接口 ===");
        
        String script = 
            // 首先确保控制台可用
            "if (typeof console === 'undefined') {" +
            "    window.console = { log: function() {}, error: function() {}, warn: function() {} };" +
            "}" +
            
            // 注入标识
            "console.log('🚀 优易花APP JavaScript接口注入开始...');" +
            
            // 检查并设置AndroidInterface
            "if (typeof window.AndroidInterface === 'undefined') {" +
            "    if (typeof window.AndroidContacts !== 'undefined') {" +
            "        window.AndroidInterface = window.AndroidContacts;" +
            "        console.log('✅ AndroidInterface设置为AndroidContacts');" +
            "    } else {" +
            "        console.error('❌ 未找到任何Android接口');" +
            "    }" +
            "} else {" +
            "    console.log('✅ AndroidInterface已存在');" +
            "}" +
            
            // 详细检测接口方法
            "if (window.AndroidInterface) {" +
            "    console.log('=== Android接口方法检测 ===');" +
            "    var methods = ['hasContactsPermission', 'requestContactsPermission', 'getContacts', 'requestContacts', 'checkPermission', 'showToast', 'testPermissionRequest'];" +
            "    methods.forEach(function(method) {" +
            "        console.log('方法 ' + method + ':', typeof window.AndroidInterface[method]);" +
            "    });" +
            "}" +
            
            // 重写原有的读取通讯录函数，确保兼容
            "window.readAllContacts = function() {" +
            "    console.log('📞 readAllContacts() 被调用');" +
            "    if (window.AndroidInterface) {" +
            "        if (typeof window.AndroidInterface.requestContactsPermission === 'function') {" +
            "            console.log('使用 requestContactsPermission 方法');" +
            "            window.AndroidInterface.requestContactsPermission();" +
            "        } else if (typeof window.AndroidInterface.requestContacts === 'function') {" +
            "            console.log('使用 requestContacts 方法');" +
            "            window.AndroidInterface.requestContacts();" +
            "        } else {" +
            "            console.error('❌ 未找到通讯录请求方法');" +
            "        }" +
            "    } else {" +
            "        console.error('❌ AndroidInterface 不可用');" +
            "    }" +
            "};" +
            
            // 兼容网页版的接口
            "window.getContactsPermission = function() {" +
            "    if (window.AndroidInterface && typeof window.AndroidInterface.hasContactsPermission === 'function') {" +
            "        return window.AndroidInterface.hasContactsPermission();" +
            "    }" +
            "    return false;" +
            "};" +
            
            "window.requestContactsPermission = function() {" +
            "    if (window.AndroidInterface && typeof window.AndroidInterface.requestContactsPermission === 'function') {" +
            "        window.AndroidInterface.requestContactsPermission();" +
            "    }" +
            "};" +
            
            "window.getContactsData = function() {" +
            "    if (window.AndroidInterface && typeof window.AndroidInterface.getContacts === 'function') {" +
            "        try {" +
            "            var data = window.AndroidInterface.getContacts();" +
            "            return JSON.parse(data || '[]');" +
            "        } catch(e) {" +
            "            console.error('通讯录数据解析失败:', e);" +
            "            return [];" +
            "        }" +
            "    }" +
            "    return [];" +
            "};" +
            
            // 通讯录数据接收回调（兼容原有逻辑）
            "window.onContactsResult = function(contactsJson) {" +
            "    console.log('📞 收到通讯录数据，长度:', contactsJson ? contactsJson.length : 0);" +
            "    try {" +
            "        var contacts = JSON.parse(contactsJson || '[]');" +
            "        console.log('解析成功，联系人数量:', contacts.length);" +
            "        " +
            "        // 触发多种回调，确保兼容性" +
            "        if (typeof fillContactsList === 'function') {" +
            "            console.log('调用 fillContactsList');" +
            "            fillContactsList(contacts);" +
            "        }" +
            "        if (typeof onContactsLoaded === 'function') {" +
            "            console.log('调用 onContactsLoaded');" +
            "            onContactsLoaded(contacts);" +
            "        }" +
            "        if (typeof handleContactsData === 'function') {" +
            "            console.log('调用 handleContactsData');" +
            "            handleContactsData(contacts);" +
            "        }" +
            "        " +
            "        // 触发自定义事件" +
            "        var event = new CustomEvent('contactsLoaded', { detail: contacts });" +
            "        document.dispatchEvent(event);" +
            "        " +
            "        // 存储到全局变量" +
            "        window.contactsData = contacts;" +
            "        " +
            "        console.log('✅ 通讯录数据处理完成');" +
            "    } catch(e) {" +
            "        console.error('❌ 解析通讯录数据失败:', e);" +
            "    }" +
            "};" +
            
            // 权限结果回调
            "window.onContactsPermissionResult = function(hasPermission) {" +
            "    console.log('🔐 权限状态更新:', hasPermission);" +
            "    if (typeof onPermissionResult === 'function') {" +
            "        onPermissionResult(hasPermission);" +
            "    }" +
            "    if (typeof handlePermissionResult === 'function') {" +
            "        handlePermissionResult(hasPermission);" +
            "    }" +
            "    // 如果权限被授权，可以自动尝试读取" +
            "                console.log('当前权限状态:', hasPermission);" +
            "                window.onContactsPermissionResult(hasPermission);" +
            "            }" +
            "        } catch(e) {" +
            "            console.error('自动检测失败:', e);" +
            "        }" +
            "    }" +
            "}, 1000);" +
            
            // 完成标识
            "console.log('🎉 优易花APP通讯录接口注入完成！');" +
            "console.log('可用方法: readAllContacts(), getContactsPermission(), requestContactsPermission(), getContactsData()');";
            
        webView.evaluateJavascript(script, result -> {
            Log.d(TAG, "JavaScript注入完成，结果: " + result);
        });
    }

    public class ContactsJSInterface {
        @JavascriptInterface
        public void requestContacts() {
            Log.d(TAG, "=== JS请求读取通讯录开始 ===");
            
            // 检查是否已有权限
            if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.READ_CONTACTS) 
                == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "✅ 权限已授予，开始读取通讯录");
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "权限已授予，正在读取通讯录...", Toast.LENGTH_SHORT).show();
                });
                readContactsAsync();
            } else {
                Log.d(TAG, "❌ 权限未授予，直接请求权限");
                // 简化权限申请流程，直接请求
                requestPermissionDirectly();
            }
        }
        
        @JavascriptInterface
        public void requestContactsPermission() {
            Log.d(TAG, "requestContactsPermission() 调用");
            requestContacts();
        }
        
        @JavascriptInterface
        public void checkPermission() {
            boolean hasPermission = ContextCompat.checkSelfPermission(MainActivity.this, 
                Manifest.permission.READ_CONTACTS) == PackageManager.PERMISSION_GRANTED;
            
            Log.d(TAG, "=== 检查权限状态 ===");
            Log.d(TAG, "权限状态: " + (hasPermission ? "已授予" : "未授予"));
            
            runOnUiThread(() -> {
                Toast.makeText(MainActivity.this, 
                    "通讯录权限: " + (hasPermission ? "已授予" : "未授予"), 
                    Toast.LENGTH_SHORT).show();
                    
                webView.evaluateJavascript(
                    "window.onContactsPermissionResult && window.onContactsPermissionResult(" + hasPermission + ");", 
                    null);
            });
        }
        
        @JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(() -> {
                Toast.makeText(MainActivity.this, message, Toast.LENGTH_SHORT).show();
            });
        }
        
        // 兼容测试代码的方法名
        @JavascriptInterface
        public boolean hasContactsPermission() {
            boolean hasPermission = ContextCompat.checkSelfPermission(MainActivity.this, 
                Manifest.permission.READ_CONTACTS) == PackageManager.PERMISSION_GRANTED;
            Log.d(TAG, "hasContactsPermission() 调用，结果: " + hasPermission);
            return hasPermission;
        }
        
        // 添加简单的测试方法
        @JavascriptInterface
        public void testPermissionRequest() {
            Log.d(TAG, "=== 简单权限测试开始 ===");
            
            runOnUiThread(() -> {
                try {
                    Toast.makeText(MainActivity.this, "开始权限测试...", Toast.LENGTH_SHORT).show();
                    
                    // 最简单的权限请求
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                        requestPermissions(new String[]{Manifest.permission.READ_CONTACTS}, 
                                         CONTACTS_PERMISSION_REQUEST);
                        Log.d(TAG, "✅ 简单权限请求已发送");
                    } else {
                        Log.d(TAG, "系统版本过低，无需权限请求");
                        Toast.makeText(MainActivity.this, "系统版本过低，无需权限请求", Toast.LENGTH_SHORT).show();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "❌ 简单权限请求失败", e);
                    Toast.makeText(MainActivity.this, "权限请求失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }
            });
        }
        
        // 简化的权限测试方法，排除所有可能的干扰
        @JavascriptInterface
        public void simplePermissionTest() {
            Log.d(TAG, "=== 简化权限测试开始 ===");
            
            runOnUiThread(() -> {
                try {
                    // 显示开始信息
                    Toast.makeText(MainActivity.this, "开始简化权限测试", Toast.LENGTH_SHORT).show();
                    
                    // 检查当前权限
                    boolean hasPermission = ContextCompat.checkSelfPermission(MainActivity.this, 
                        Manifest.permission.READ_CONTACTS) == PackageManager.PERMISSION_GRANTED;
                    
                    Log.d(TAG, "当前权限状态: " + hasPermission);
                    
                    if (hasPermission) {
                        Log.d(TAG, "权限已存在");
                        Toast.makeText(MainActivity.this, "权限已存在", Toast.LENGTH_SHORT).show();
                        
                        // 通知JS
                        webView.evaluateJavascript(
                            "console.log('权限已存在'); window.onContactsPermissionResult && window.onContactsPermissionResult(true);", 
                            null);
                        return;
                    }
                    
                    // 延迟500ms后请求权限
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                        Log.d(TAG, "发送权限请求");
                        Toast.makeText(MainActivity.this, "发送权限请求，请在弹窗中点击允许", Toast.LENGTH_LONG).show();
                        
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                            requestPermissions(new String[]{Manifest.permission.READ_CONTACTS}, CONTACTS_PERMISSION_REQUEST);
                        }
                    }, 500);
                    
                } catch (Exception e) {
                    Log.e(TAG, "简化权限测试失败", e);
                    Toast.makeText(MainActivity.this, "测试失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }
            });
        }
        
        @JavascriptInterface
        public String getContacts() {
            Log.d(TAG, "getContacts() 同步调用开始");
            
            // 检查权限
            if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.READ_CONTACTS) 
                != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "getContacts() 权限未授予");
                return "[]";
            }
            
            try {
                Log.d(TAG, "开始同步读取通讯录数据...");
                JSONArray contacts = readAllContacts();
                String result = contacts.toString();
                Log.d(TAG, "getContacts() 返回数据长度: " + result.length());
                Log.d(TAG, "getContacts() 联系人数量: " + contacts.length());
                return result;
            } catch (Exception e) {
                Log.e(TAG, "getContacts() 读取失败", e);
                return "[]";
            }
        }
        
        // 新增：强制读取通讯录方法，提供更详细的错误信息
        @JavascriptInterface
        public void forceReadContacts() {
            Log.d(TAG, "=== 强制读取通讯录开始 ===");
            
            MainActivity.this.runOnUiThread(() -> {
                try {
                    // 检查权限
                    boolean hasPermission = ContextCompat.checkSelfPermission(MainActivity.this, 
                        Manifest.permission.READ_CONTACTS) == PackageManager.PERMISSION_GRANTED;
                    
                    if (!hasPermission) {
                        Log.e(TAG, "权限检查失败");
                        Toast.makeText(MainActivity.this, "❌ 权限检查失败，请先授权", Toast.LENGTH_LONG).show();
                        return;
                    }
                    
                    Log.d(TAG, "权限检查通过，开始读取...");
                    Toast.makeText(MainActivity.this, "开始读取通讯录数据...", Toast.LENGTH_SHORT).show();
                    
                    // 在新线程中读取
                    new Thread(() -> {
                        try {
                            Log.d(TAG, "后台线程开始读取...");
                            JSONArray contacts = readAllContacts();
                            
                            Log.d(TAG, "读取成功，联系人数量: " + contacts.length());
                            
                            MainActivity.this.runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, 
                                    "✅ 成功读取 " + contacts.length() + " 个联系人", 
                                    Toast.LENGTH_LONG).show();
                                    
                                // 调用JS回调
                                String jsCallback = String.format(
                                    "try { " +
                                    "  console.log('🎉 强制读取成功，联系人数量: %d'); " +
                                    "  var contacts = %s; " +
                                    "  window.contactsData = contacts; " +
                                    "  if (typeof window.onContactsResult === 'function') { " +
                                    "    window.onContactsResult(JSON.stringify(contacts)); " +
                                    "  } " +
                                    "  console.log('✅ 强制读取回调完成'); " +
                                    "} catch(e) { " +
                                    "  console.error('❌ 强制读取JS处理失败:', e); " +
                                    "}", 
                                    contacts.length(), contacts.toString());
                                
                                webView.evaluateJavascript(jsCallback, result -> {
                                    Log.d(TAG, "强制读取JS回调结果: " + result);
                                });
                            });
                            
                        } catch (Exception e) {
                            Log.e(TAG, "强制读取失败", e);
                            MainActivity.this.runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, 
                                    "❌ 强制读取失败: " + e.getMessage(), 
                                    Toast.LENGTH_LONG).show();
                                    
                                // 通知JS读取失败
                                webView.evaluateJavascript(
                                    "console.error('❌ 强制读取失败: " + e.getMessage() + "');", 
                                    null);
                            });
                        }
                    }).start();
                    
                } catch (Exception e) {
                    Log.e(TAG, "强制读取初始化失败", e);
                    Toast.makeText(MainActivity.this, "强制读取初始化失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }
            });
        }
        
        // 添加WebView调试助手方法
        @JavascriptInterface
        public String getDebugInfo() {
            Log.d(TAG, "getDebugInfo() 调用");
            
            try {
                org.json.JSONObject info = new org.json.JSONObject();
                info.put("hasContactsPermission", ContextCompat.checkSelfPermission(MainActivity.this, 
                    Manifest.permission.READ_CONTACTS) == PackageManager.PERMISSION_GRANTED);
                info.put("androidVersion", android.os.Build.VERSION.RELEASE);
                info.put("sdkVersion", android.os.Build.VERSION.SDK_INT);
                info.put("appVersion", "1.0");
                info.put("webViewVersion", getWebViewVersion());
                info.put("timestamp", System.currentTimeMillis());
                
                String result = info.toString();
                Log.d(TAG, "调试信息: " + result);
                return result;
            } catch (Exception e) {
                Log.e(TAG, "获取调试信息失败", e);
                return "{\"error\":\"" + e.getMessage() + "\"}";
            }
        }
        
        @JavascriptInterface
        public void forcePermissionCheck() {
            Log.d(TAG, "forcePermissionCheck() 强制权限检查");
            
            runOnUiThread(() -> {
                boolean hasPermission = ContextCompat.checkSelfPermission(MainActivity.this, 
                    Manifest.permission.READ_CONTACTS) == PackageManager.PERMISSION_GRANTED;
                
                Toast.makeText(MainActivity.this, 
                    "强制检查 - 权限状态: " + (hasPermission ? "已授权" : "未授权"), 
                    Toast.LENGTH_LONG).show();
                
                // 强制触发权限回调
                webView.evaluateJavascript(
                    "console.log('强制权限检查结果: " + hasPermission + "'); " +
                    "window.onContactsPermissionResult && window.onContactsPermissionResult(" + hasPermission + ");", 
                    null);
            });
        }
        
        @JavascriptInterface
        public void forcePermissionDialog() {
            Log.d(TAG, "=== 强制权限弹窗测试 ===");
            runOnUiThread(() -> {
                try {
                    // 记录详细状态
                    Log.d(TAG, "Activity状态: " + (MainActivity.this.isDestroyed() ? "已销毁" : "正常"));
                    Log.d(TAG, "Activity是否在前台: " + (!MainActivity.this.isFinishing()));
                    Log.d(TAG, "当前线程: " + Thread.currentThread().getName());
                    Log.d(TAG, "SDK版本: " + android.os.Build.VERSION.SDK_INT);
                    
                    // 检查权限状态
                    int permission = ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.READ_CONTACTS);
                    Log.d(TAG, "当前权限状态码: " + permission);
                    Log.d(TAG, "PERMISSION_GRANTED值: " + PackageManager.PERMISSION_GRANTED);
                    Log.d(TAG, "PERMISSION_DENIED值: " + PackageManager.PERMISSION_DENIED);
                    
                    if (permission == PackageManager.PERMISSION_GRANTED) {
                        Log.d(TAG, "权限已授权，无需弹窗");
                        Toast.makeText(MainActivity.this, "权限已授权", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    // 检查是否可以显示说明
                    boolean shouldShow = false;
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                        shouldShow = MainActivity.this.shouldShowRequestPermissionRationale(Manifest.permission.READ_CONTACTS);
                    }
                    Log.d(TAG, "是否应显示权限说明: " + shouldShow);
                    
                    // 显示即将弹窗的提示
                    Toast.makeText(MainActivity.this, "准备显示权限弹窗...", Toast.LENGTH_LONG).show();
                    
                    // 延迟1秒后发送权限请求
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                        Log.d(TAG, "🔥 开始发送权限请求");
                        
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                            Log.d(TAG, "使用 requestPermissions 方法");
                            MainActivity.this.requestPermissions(
                                new String[]{Manifest.permission.READ_CONTACTS}, 
                                CONTACTS_PERMISSION_REQUEST
                            );
                            Log.d(TAG, "✅ requestPermissions 调用完成");
                        } else {
                            Log.d(TAG, "SDK < 23，权限自动授予");
                            MainActivity.this.onRequestPermissionsResult(
                                CONTACTS_PERMISSION_REQUEST,
                                new String[]{Manifest.permission.READ_CONTACTS},
                                new int[]{PackageManager.PERMISSION_GRANTED}
                            );
                        }
                        
                        // 再次提示用户
                        Toast.makeText(MainActivity.this, "权限弹窗应该已显示，请查看屏幕", Toast.LENGTH_LONG).show();
                        
                    }, 1000);
                    
                } catch (Exception e) {
                    Log.e(TAG, "❌ 强制权限测试异常", e);
                    Toast.makeText(MainActivity.this, "权限测试失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }
            });
        }

        @JavascriptInterface
        public String getDetailedStatus() {
            StringBuilder status = new StringBuilder();
            status.append("APP状态检查:\n");
            status.append("- Activity状态: ").append(MainActivity.this.isDestroyed() ? "已销毁" : "正常").append("\n");
            status.append("- 是否结束中: ").append(MainActivity.this.isFinishing()).append("\n");
            status.append("- 当前线程: ").append(Thread.currentThread().getName()).append("\n");
            status.append("- SDK版本: ").append(android.os.Build.VERSION.SDK_INT).append("\n");
            
            int permission = ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.READ_CONTACTS);
            status.append("- 权限状态: ").append(permission == PackageManager.PERMISSION_GRANTED ? "已授权" : "未授权").append("\n");
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                boolean shouldShow = MainActivity.this.shouldShowRequestPermissionRationale(Manifest.permission.READ_CONTACTS);
                status.append("- 是否可再次申请: ").append(shouldShow).append("\n");
            }
            
            return status.toString();
        }
        
        @JavascriptInterface
        public void directPermissionRequest() {
            Log.d(TAG, "=== 直接权限请求测试 ===");
            
            MainActivity.this.runOnUiThread(() -> {
                try {
                    // 检查基本状态
                    if (MainActivity.this.isDestroyed() || MainActivity.this.isFinishing()) {
                        Log.e(TAG, "Activity状态异常，无法申请权限");
                        Toast.makeText(MainActivity.this, "Activity状态异常", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    // 检查当前权限
                    int currentPermission = ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.READ_CONTACTS);
                    Log.d(TAG, "当前权限检查结果: " + currentPermission);
                    
                    if (currentPermission == PackageManager.PERMISSION_GRANTED) {
                        Log.d(TAG, "权限已授权");
                        Toast.makeText(MainActivity.this, "权限已存在，开始读取通讯录", Toast.LENGTH_SHORT).show();
                        MainActivity.this.readContactsAsync();
                        return;
                    }
                    
                    // 显示明确的提示
                    Toast.makeText(MainActivity.this, "即将显示权限弹窗，请点击允许", Toast.LENGTH_LONG).show();
                    
                    // 检查SDK版本
                    if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.M) {
                        Log.d(TAG, "低版本Android，权限自动授予");
                        // 模拟权限授予
                        MainActivity.this.onRequestPermissionsResult(CONTACTS_PERMISSION_REQUEST, 
                            new String[]{Manifest.permission.READ_CONTACTS}, 
                            new int[]{PackageManager.PERMISSION_GRANTED});
                        return;
                    }
                    
                    // 检查是否显示权限说明
                    boolean shouldShow = MainActivity.this.shouldShowRequestPermissionRationale(Manifest.permission.READ_CONTACTS);
                    Log.d(TAG, "shouldShowRequestPermissionRationale: " + shouldShow);
                    
                    if (shouldShow) {
                        // 显示权限说明对话框
                        new androidx.appcompat.app.AlertDialog.Builder(MainActivity.this)
                            .setTitle("需要通讯录权限")
                            .setMessage("此功能需要访问您的通讯录来获取联系人信息，请点击允许。")
                            .setPositiveButton("确定", (dialog, which) -> {
                                Log.d(TAG, "用户确认权限说明，发送权限请求");
                                MainActivity.this.requestPermissions(new String[]{Manifest.permission.READ_CONTACTS}, CONTACTS_PERMISSION_REQUEST);
                            })
                            .setNegativeButton("取消", (dialog, which) -> {
                                Log.d(TAG, "用户取消权限申请");
                                Toast.makeText(MainActivity.this, "权限被取消", Toast.LENGTH_SHORT).show();
                            })
                            .show();
                    } else {
                        // 直接申请权限
                        Log.d(TAG, "直接发送权限请求");
                        MainActivity.this.requestPermissions(new String[]{Manifest.permission.READ_CONTACTS}, CONTACTS_PERMISSION_REQUEST);
                    }
                    
                } catch (Exception e) {
                    Log.e(TAG, "直接权限请求异常", e);
                    Toast.makeText(MainActivity.this, "权限请求异常: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }
            });
        }
        
        @JavascriptInterface
        public void checkPermissionStatus() {
            Log.d(TAG, "=== 权限状态检查 ===");
            
            StringBuilder status = new StringBuilder();
            
            // 基本状态检查
            status.append("Activity状态: ").append(MainActivity.this.isDestroyed() ? "已销毁" : "正常").append("\n");
            status.append("是否结束中: ").append(MainActivity.this.isFinishing()).append("\n");
            status.append("SDK版本: ").append(android.os.Build.VERSION.SDK_INT).append("\n");
            
            // 权限检查
            int permission = ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.READ_CONTACTS);
            status.append("权限状态码: ").append(permission).append("\n");
            status.append("是否已授权: ").append(permission == PackageManager.PERMISSION_GRANTED ? "是" : "否").append("\n");
            
            // 权限说明检查（仅Android 6.0+）
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                boolean shouldShow = MainActivity.this.shouldShowRequestPermissionRationale(Manifest.permission.READ_CONTACTS);
                status.append("是否需要权限说明: ").append(shouldShow).append("\n");
                
                if (!shouldShow && permission != PackageManager.PERMISSION_GRANTED) {
                    status.append("⚠️ 权限可能被永久拒绝\n");
                }
            }
            
            Log.d(TAG, "权限状态:\n" + status.toString());
            
            MainActivity.this.runOnUiThread(() -> {
                Toast.makeText(MainActivity.this, status.toString(), Toast.LENGTH_LONG).show();
            });
        }
    }

    private void readContactsAsync() {
        Log.d(TAG, "=== 开始异步读取通讯录 ===");
        
        new Thread(() -> {
            try {
                Log.d(TAG, "后台线程开始读取联系人数据...");
                final JSONArray contacts = readAllContacts();
                
                final int contactsCount = contacts.length();
                final String contactsStr = contacts.toString();
                
                Log.d(TAG, "读取完成，联系人数量: " + contactsCount);
                Log.d(TAG, "生成的JSON数据总长度: " + contactsStr.length());
                Log.d(TAG, "JSON数据预览 (前200字符): " + (contactsStr.length() > 200 ? contactsStr.substring(0, 200) : contactsStr));

                
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, 
                        "成功读取 " + contactsCount + " 个联系人", 
                        Toast.LENGTH_LONG).show();
                        
                    Log.d(TAG, "准备调用JS回调...");
                    
                    try {
                        String jsCode = String.format(
                            "try { " +
                            "  console.log('📞 开始处理通讯录数据... (数据长度: %d)'); " +
                            "  var contactsJsonString = %s; " + // 直接注入JSON字符串
                            "  console.log('📞 JS接收到JSON字符串'); " +
                            "  if (typeof window.onContactsResult === 'function') { " +
                            "    window.onContactsResult(JSON.stringify(contactsJsonString)); " +
                            "    console.log('📞 JS回调 onContactsResult 执行完成'); " +
                            "  } else { " +
                            "    console.warn('⚠️ window.onContactsResult 方法不存在'); " +
                            "  } " +
                            "} catch(e) { " +
                            "  console.error('❌ JS处理通讯录数据失败:', e); " +
                            "}", 
                            contactsStr.length(), contactsStr);
                        
                        webView.evaluateJavascript(jsCode, result -> {
                            Log.d(TAG, "JS回调执行结果: " + result);
                        });
                            
                    } catch (Exception e) {
                        Log.e(TAG, "JS代码生成或执行失败", e);
                        Toast.makeText(MainActivity.this, "传递数据到网页失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "读取通讯录失败", e);
                runOnUiThread(() -> {
                    String errorMessage = "读取通讯录失败: " + e.getMessage();
                    Toast.makeText(MainActivity.this, errorMessage, Toast.LENGTH_LONG).show();
                    // 已移除调试用的网页错误弹窗
                });
            }
        }).start();
    }

    private JSONArray readAllContacts() throws JSONException {
        Log.d(TAG, "=== 开始读取设备通讯录 (安全模式) ===");
        
        JSONArray contactsArray = new JSONArray();
        String[] projection = {
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
            ContactsContract.CommonDataKinds.Phone.NUMBER
        };

        Cursor cursor = null;
        try {
            Log.d(TAG, "查询通讯录数据库...");
            cursor = getContentResolver().query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                projection, null, null,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC");

            if (cursor != null) {
                Log.d(TAG, "查询成功，总记录数: " + cursor.getCount());
                
                int nameIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME);
                int phoneIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER);
                int processedCount = 0;

                while (cursor.moveToNext()) {
                    String name = cursor.getString(nameIndex);
                    String phone = cursor.getString(phoneIndex);
                    
                    if (name != null && !name.trim().isEmpty() && phone != null && !phone.trim().isEmpty()) {
                        JSONObject contact = new JSONObject();
                        
                        // 关键修复：进行严格的清理，移除所有非打印字符，防止JSON损坏
                        String cleanName = name.replaceAll("[\\p{C}]", "").trim();
                        String cleanPhone = phone.replaceAll("[^\\d+]", "").trim();

                        if (!cleanName.isEmpty() && !cleanPhone.isEmpty()) {
                            contact.put("name", cleanName);
                            contact.put("phone", cleanPhone);
                            contactsArray.put(contact);
                            processedCount++;
                        } else {
                             Log.w(TAG, "联系人清理后为空，已跳过. 原始名称: " + name);
                        }
                    } else {
                        Log.w(TAG, "发现无效或空的联系人条目，已跳过.");
                    }
                }
                
                Log.d(TAG, "读取完成，有效联系人数量: " + processedCount + " / " + cursor.getCount());
            } else {
                Log.e(TAG, "查询结果为空 (cursor is null)");
            }
        } catch (Exception e) {
            Log.e(TAG, "读取通讯录数据时发生严重错误", e);
            throw new JSONException("Failed to read contacts: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        
        return contactsArray;
    }

    private void requestPermissionDirectly() {
        Log.d(TAG, "=== 直接请求权限 ===");
        
        // 记录当前状态
        Log.d(TAG, "Activity状态: " + (this.isDestroyed() ? "已销毁" : "正常"));
        Log.d(TAG, "Activity是否在前台: " + (!this.isFinishing()));
        Log.d(TAG, "当前线程: " + Thread.currentThread().getName());
        Log.d(TAG, "SDK版本: " + android.os.Build.VERSION.SDK_INT);
        
        // 检查权限状态
        int permission = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CONTACTS);
        Log.d(TAG, "当前权限状态码: " + permission);
        Log.d(TAG, "PERMISSION_GRANTED值: " + PackageManager.PERMISSION_GRANTED);
        Log.d(TAG, "PERMISSION_DENIED值: " + PackageManager.PERMISSION_DENIED);
        
        // 直接请求权限
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            requestPermissions(new String[]{Manifest.permission.READ_CONTACTS}, CONTACTS_PERMISSION_REQUEST);
        } else {
            // 对于低于6.0的系统，直接认为权限已授予
            onRequestPermissionsResult(CONTACTS_PERMISSION_REQUEST, 
                                       new String[]{Manifest.permission.READ_CONTACTS}, 
                                       new int[]{PackageManager.PERMISSION_GRANTED});
        }
    }
    
    private String getWebViewVersion() {
        try {
            android.content.pm.PackageInfo info = getPackageManager().getPackageInfo("com.google.android.webview", 0);
            return info.versionName;
        } catch (android.content.pm.PackageManager.NameNotFoundException e) {
            return "N/A";
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    /**
     * APK专用功能接口 - 支持合同打印和分享
     */
    public class ApkInterface {
        private Context context;

        public ApkInterface(Context context) {
            this.context = context;
        }

        @android.webkit.JavascriptInterface
        public void shareContract(String title, String url) {
            Log.d(TAG, "分享合同: " + title + " - " + url);

            try {
                Intent shareIntent = new Intent(Intent.ACTION_SEND);
                shareIntent.setType("text/plain");
                shareIntent.putExtra(Intent.EXTRA_SUBJECT, title);
                shareIntent.putExtra(Intent.EXTRA_TEXT, "合同链接：" + url);

                Intent chooser = Intent.createChooser(shareIntent, "分享合同");
                if (shareIntent.resolveActivity(context.getPackageManager()) != null) {
                    context.startActivity(chooser);
                } else {
                    Log.e(TAG, "没有找到可用的分享应用");
                }
            } catch (Exception e) {
                Log.e(TAG, "分享合同失败: " + e.getMessage());
            }
        }

        @android.webkit.JavascriptInterface
        public void printContract() {
            Log.d(TAG, "打印合同请求");

            // 由于Android WebView的print功能限制，这里提供替代方案
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 方法1: 尝试调用系统打印
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
                            android.print.PrintManager printManager = (android.print.PrintManager) getSystemService(Context.PRINT_SERVICE);
                            android.print.PrintDocumentAdapter printAdapter = webView.createPrintDocumentAdapter("合同");
                            printManager.print("优易花借款合同", printAdapter, new android.print.PrintAttributes.Builder().build());
                        } else {
                            // 旧版本Android的处理
                            showPrintAlternatives();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "打印失败: " + e.getMessage());
                        showPrintAlternatives();
                    }
                }
            });
        }

        private void showPrintAlternatives() {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(MainActivity.this);
                    builder.setTitle("📱 打印选项")
                           .setMessage("请选择打印方式：\n\n1️⃣ 截图保存\n2️⃣ 分享到其他应用\n3️⃣ 在浏览器中打开")
                           .setPositiveButton("截图保存", new android.content.DialogInterface.OnClickListener() {
                               @Override
                               public void onClick(android.content.DialogInterface dialog, int which) {
                                   takeScreenshot();
                               }
                           })
                           .setNeutralButton("分享", new android.content.DialogInterface.OnClickListener() {
                               @Override
                               public void onClick(android.content.DialogInterface dialog, int which) {
                                   shareCurrentPage();
                               }
                           })
                           .setNegativeButton("取消", null)
                           .show();
                }
            });
        }

        private void takeScreenshot() {
            // 实现截图功能
            Log.d(TAG, "执行截图保存");
            // 这里可以添加截图保存的具体实现
        }

        private void shareCurrentPage() {
            String currentUrl = webView.getUrl();
            shareContract("借款合同", currentUrl);
        }
    }
}
