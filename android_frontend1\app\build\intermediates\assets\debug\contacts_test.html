<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通讯录功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #FF6B35; color: white; border: none; border-radius: 5px; }
        .contact { padding: 10px; border-bottom: 1px solid #eee; }
        .contact-name { font-weight: bold; }
        .contact-phone { color: #666; font-size: 14px; }
        #contactsList { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; margin-top: 10px; }
        .status { padding: 10px; background: #f0f0f0; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>📱 随意花通讯录功能测试</h1>
    
    <div class="status" id="status">等待操作...</div>
    
    <button onclick="checkPermission()">🔐 检查权限</button>
    <button onclick="loadContacts()">📞 读取通讯录</button>
    <button onclick="clearList()">🗑️ 清空列表</button>
    
    <div id="contactsList"></div>
    
    <script>
        // 检查权限状态
        function checkPermission() {
            updateStatus('正在检查权限...');
            
            if (typeof ContactsManager !== 'undefined') {
                ContactsManager.checkPermission(function(hasPermission) {
                    updateStatus(hasPermission ? '✅ 已获得通讯录权限' : '❌ 未获得通讯录权限');
                });
            } else {
                updateStatus('⚠️ 通讯录接口不可用（请在APP中打开）');
            }
        }
        
        // 读取通讯录
        function loadContacts() {
            updateStatus('正在读取通讯录...');
            
            if (typeof ContactsManager !== 'undefined') {
                ContactsManager.readAll(
                    function(contacts) {
                        updateStatus(`✅ 成功读取 ${contacts.length} 条通讯录`);
                        displayContacts(contacts);
                    },
                    function(error) {
                        updateStatus('❌ 读取失败: ' + error.message);
                    }
                );
            } else {
                updateStatus('⚠️ 通讯录接口不可用（请在APP中打开）');
            }
        }
        
        // 显示通讯录列表
        function displayContacts(contacts) {
            const listDiv = document.getElementById('contactsList');
            listDiv.innerHTML = '';
            
            if (contacts.length === 0) {
                listDiv.innerHTML = '<div class="contact">暂无通讯录数据</div>';
                return;
            }
            
            // 格式化并排序
            const formattedContacts = ContactsManager ? 
                ContactsManager.formatContacts(contacts) : 
                contacts;
            
            formattedContacts.forEach(contact => {
                const contactDiv = document.createElement('div');
                contactDiv.className = 'contact';
                contactDiv.innerHTML = `
                    <div class="contact-name">${contact.name}</div>
                    <div class="contact-phone">${contact.phone}</div>
                `;
                listDiv.appendChild(contactDiv);
            });
        }
        
        // 清空列表
        function clearList() {
            document.getElementById('contactsList').innerHTML = '';
            updateStatus('列表已清空');
        }
        
        // 更新状态显示
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，可以开始测试');
            
            // 监听通讯录加载事件
            document.addEventListener('contactsLoaded', function(event) {
                console.log('收到通讯录加载事件:', event.detail.length, '条');
            });
        });
        
        // 使用Promise方式的示例
        async function loadContactsAsync() {
            try {
                updateStatus('使用Promise方式读取通讯录...');
                
                if (typeof getContacts === 'function') {
                    const contacts = await getContacts();
                    updateStatus(`✅ Promise方式成功读取 ${contacts.length} 条通讯录`);
                    displayContacts(contacts);
                } else {
                    updateStatus('⚠️ Promise接口不可用');
                }
            } catch (error) {
                updateStatus('❌ Promise读取失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
