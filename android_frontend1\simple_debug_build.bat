@echo off
chcp 65001 >nul
echo 🚀 简单Debug编译（跳过签名问题）
echo ================================

echo 清理缓存...
if exist ".gradle" rmdir /s /q ".gradle" >nul 2>&1
if exist "app\build" rmdir /s /q "app\build" >nul 2>&1

echo 开始编译...
gradlew.bat clean assembleDebug --no-daemon --stacktrace

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ 编译成功！
    echo APK: app\build\outputs\apk\debug\app-debug.apk
) else (
    echo ❌ 编译失败
)

pause
