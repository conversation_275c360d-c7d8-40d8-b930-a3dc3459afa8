<!-- ====================================================== -->
<!-- 随意花APP通讯录测试代码 - 添加到 https://dailuanshej.cn -->
<!-- ====================================================== -->

<script>
// 通讯录测试功能
(function() {
    'use strict';
    
    // 检测APP环境
    const isInApp = typeof AndroidContacts !== 'undefined';
    
    // 创建测试按钮
    function createTestButton() {
        if (document.getElementById('contacts-test-btn')) return;
        
        const btn = document.createElement('button');
        btn.id = 'contacts-test-btn';
        btn.innerHTML = '📱 测试通讯录';
        btn.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${isInApp ? '#28a745' : '#6c757d'};
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            transition: all 0.3s ease;
        `;
        
        btn.onclick = isInApp ? showTestPanel : showAppRequired;
        document.body.appendChild(btn);
    }
    
    // 显示测试面板
    function showTestPanel() {
        const panel = document.createElement('div');
        panel.id = 'test-panel';
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 400px;
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
            z-index: 10000;
        `;
        
        panel.innerHTML = `
            <h3 style="margin: 0 0 20px 0; color: #007bff; text-align: center;">📱 通讯录功能测试</h3>
            
            <div style="margin-bottom: 15px; padding: 10px; background: #d4edda; border-radius: 8px; color: #155724; text-align: center;">
                ✅ 检测到APP环境
            </div>
            
            <button onclick="testPermission()" style="width: 100%; margin-bottom: 10px; padding: 12px; background: #007bff; color: white; border: none; border-radius: 8px; font-size: 16px; cursor: pointer;">
                🔐 检查权限状态
            </button>
            
            <button onclick="testReadContacts()" style="width: 100%; margin-bottom: 10px; padding: 12px; background: #28a745; color: white; border: none; border-radius: 8px; font-size: 16px; cursor: pointer;">
                📞 读取通讯录
            </button>
            
            <button onclick="testDialogResponse()" style="width: 100%; margin-bottom: 20px; padding: 12px; background: #ffc107; color: #212529; border: none; border-radius: 8px; font-size: 16px; cursor: pointer;">
                🔧 测试权限对话框
            </button>
            
            <div id="test-result" style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 8px; font-size: 14px; min-height: 50px; display: none;">
                <strong>测试结果:</strong><br>
                <span id="result-text"></span>
            </div>
            
            <button onclick="closeTestPanel()" style="width: 100%; padding: 10px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
                关闭测试
            </button>
        `;
        
        document.body.appendChild(panel);
    }
    
    // 显示需要APP环境的提示
    function showAppRequired() {
        alert('请在随意花APP中打开此页面进行通讯录功能测试！');
    }
    
    // 测试权限
    window.testPermission = function() {
        showResult('正在检查通讯录权限...', 'info');
        if (AndroidContacts && AndroidContacts.checkPermission) {
            AndroidContacts.checkPermission();
        } else {
            showResult('错误: 权限检查接口不可用', 'error');
        }
    };
    
    // 测试读取通讯录
    window.testReadContacts = function() {
        showResult('正在请求读取通讯录...', 'info');
        if (AndroidContacts && AndroidContacts.requestContacts) {
            AndroidContacts.requestContacts();
        } else {
            showResult('错误: 通讯录读取接口不可用', 'error');
        }
    };
    
    // 测试权限对话框响应
    window.testDialogResponse = function() {
        showResult('测试权限对话框响应...', 'info');
        if (AndroidContacts) {
            // 先检查权限
            AndroidContacts.checkPermission();
            // 2秒后请求权限
            setTimeout(() => {
                showResult('发送权限请求，请注意对话框是否可点击...', 'info');
                AndroidContacts.requestContacts();
            }, 2000);
        } else {
            showResult('错误: AndroidContacts接口不可用', 'error');
        }
    };
    
    // 显示测试结果
    function showResult(message, type = 'info') {
        const resultDiv = document.getElementById('test-result');
        const resultText = document.getElementById('result-text');
        
        if (resultDiv && resultText) {
            resultDiv.style.display = 'block';
            resultDiv.style.background = type === 'error' ? '#f8d7da' : type === 'success' ? '#d4edda' : '#e2e3e5';
            resultDiv.style.color = type === 'error' ? '#721c24' : type === 'success' ? '#155724' : '#383d41';
            
            const timestamp = new Date().toLocaleTimeString();
            resultText.innerHTML = `[${timestamp}] ${message}`;
        }
        
        console.log(`[通讯录测试] ${message}`);
    }
    
    // 关闭测试面板
    window.closeTestPanel = function() {
        const panel = document.getElementById('test-panel');
        if (panel) panel.remove();
    };
    
    // 权限检查结果回调
    window.onContactsPermissionResult = function(hasPermission) {
        const status = hasPermission ? '已授权 ✅' : '未授权 ❌';
        const type = hasPermission ? 'success' : 'error';
        showResult(`权限状态: ${status}`, type);
    };
    
    // 通讯录数据结果回调
    window.onContactsResult = function(contactsJson) {
        try {
            const contacts = JSON.parse(contactsJson);
            showResult(`成功读取 ${contacts.length} 个联系人 ✅`, 'success');
            
            // 显示前3个联系人作为示例
            if (contacts.length > 0) {
                const examples = contacts.slice(0, 3).map(c => 
                    `${c.name}: ${c.phone}`
                ).join('<br>');
                
                setTimeout(() => {
                    showResult(`联系人示例:<br>${examples}<br>...共${contacts.length}个`, 'success');
                }, 1000);
            }
        } catch (e) {
            showResult(`数据解析失败: ${e.message} ❌`, 'error');
        }
    };
    
    // 页面加载完成后创建测试按钮
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createTestButton);
    } else {
        createTestButton();
    }
    
    console.log('🚀 随意花通讯录测试工具已加载');
    
})();
</script>

<!-- ====================================================== -->
<!-- 使用说明: -->
<!-- 1. 将上面的代码添加到您的网站页面中 -->
<!-- 2. 在随意花APP中打开 https://dailuanshej.cn -->
<!-- 3. 页面右上角会显示"📱 测试通讯录"按钮 -->
<!-- 4. 点击按钮打开测试面板，测试各种功能 -->
<!-- ====================================================== -->
