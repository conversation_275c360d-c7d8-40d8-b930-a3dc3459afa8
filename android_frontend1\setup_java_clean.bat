@echo off
echo =========================================
echo Android Studio JDK 查找和配置工具
echo =========================================
echo.

echo 正在搜索 Android Studio 安装位置...
echo.

REM 检查常见的Android Studio安装位置
set AS_FOUND=0

echo 1. 检查 Program Files...
if exist "C:\Program Files\Android\Android Studio" (
    echo [找到] C:\Program Files\Android\Android Studio
    set "AS_PATH=C:\Program Files\Android\Android Studio"
    set AS_FOUND=1
)

echo 2. 检查 Program Files ^(x86^)...
if exist "C:\Program Files (x86)\Android\Android Studio" (
    echo [找到] C:\Program Files ^(x86^)\Android\Android Studio
    set "AS_PATH=C:\Program Files (x86)\Android\Android Studio"
    set AS_FOUND=1
)

echo 3. 检查用户目录...
if exist "%LOCALAPPDATA%\Android\Sdk" (
    echo [找到] Android SDK: %LOCALAPPDATA%\Android\Sdk
)

echo 4. 检查其他可能位置...
for %%d in (C D E F) do (
    if exist "%%d:\Android\Android Studio" (
        echo [找到] %%d:\Android\Android Studio
        set "AS_PATH=%%d:\Android\Android Studio"
        set AS_FOUND=1
    )
)

echo.
if %AS_FOUND%==1 (
    echo 在 Android Studio 中查找 JDK...
    echo.
    
    REM 检查Android Studio自带的JDK位置
    if exist "%AS_PATH%\jbr" (
        echo [找到] 内置JDK: %AS_PATH%\jbr
        set "JAVA_PATH=%AS_PATH%\jbr"
        goto found_java
    )
    
    if exist "%AS_PATH%\jre" (
        echo [找到] 内置JRE: %AS_PATH%\jre
        set "JAVA_PATH=%AS_PATH%\jre"
        goto found_java
    )
    
    REM 检查子目录
    for /d %%i in ("%AS_PATH%\jbr*") do (
        if exist "%%i\bin\java.exe" (
            echo [找到] JDK: %%i
            set "JAVA_PATH=%%i"
            goto found_java
        )
    )
    
    echo [错误] 在 Android Studio 目录中未找到 JDK
    goto manual_input
) else (
    echo [错误] 未找到 Android Studio 安装目录
    goto manual_input
)

:found_java
echo.
echo [成功] 找到Java: %JAVA_PATH%
echo.
echo 测试Java版本...
"%JAVA_PATH%\bin\java.exe" -version
if %errorlevel% equ 0 (
    echo.
    echo [成功] Java可以正常运行
    echo.
    echo 现在设置环境变量进行构建...
    set "JAVA_HOME=%JAVA_PATH%"
    set "PATH=%JAVA_PATH%\bin;%PATH%"
    
    echo 环境变量已临时设置：
    echo JAVA_HOME = %JAVA_HOME%
    echo.
    
    echo 现在可以构建APK了！
    echo.
    echo 可用命令：
    echo gradlew.bat clean
    echo gradlew.bat assembleDebug
    echo.
    
    set /p run_build="是否立即开始构建？(y/n): "
    if /i "%run_build%"=="y" (
        echo.
        echo 开始清理项目...
        gradlew.bat clean
        echo.
        echo 开始构建APK...
        gradlew.bat assembleDebug
        
        if %errorlevel% equ 0 (
            echo.
            echo [成功] 构建完成！
            echo APK位置: app\build\outputs\apk\debug\app-debug.apk
            echo.
            echo 复制APK到当前目录...
            if exist "app\build\outputs\apk\debug\app-debug.apk" (
                copy "app\build\outputs\apk\debug\app-debug.apk" "随意花-调试版.apk"
                echo [完成] APK已复制为: 随意花-调试版.apk
            )
        ) else (
            echo.
            echo [错误] 构建失败，请检查错误信息
        )
    )
) else (
    echo [错误] Java无法运行
    goto manual_input
)
goto end

:manual_input
echo.
echo 手动输入 Android Studio JDK 路径
echo.
echo 请打开 Android Studio，然后：
echo 1. File - Project Structure - SDK Location
echo 2. 查看 "JDK location" 路径
echo 3. 或者在 File - Settings - Build - Build Tools - Gradle 中查看 "Gradle JVM"
echo.
set /p manual_path="请输入JDK完整路径: "

if exist "%manual_path%\bin\java.exe" (
    echo [成功] 找到Java: %manual_path%
    set "JAVA_HOME=%manual_path%"
    set "PATH=%manual_path%\bin;%PATH%"
    echo 环境变量已设置，可以进行构建
    echo.
    echo 现在运行: gradlew.bat assembleDebug
) else (
    echo [错误] 路径无效或Java不存在
)

:end
echo.
echo 提示：这个设置只在当前命令窗口有效
echo 如需永久设置，请运行：
echo setx JAVA_HOME "%JAVA_HOME%"
echo.
pause
