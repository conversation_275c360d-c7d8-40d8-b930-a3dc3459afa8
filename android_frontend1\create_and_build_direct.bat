@echo off
chcp 65001 >nul
echo ========================================
echo    Direct Build in E:\android_frontend
echo ========================================
echo.

echo [1/4] Creating E:\android_frontend directory...
mkdir "E:\android_frontend" 2>nul
if exist "E:\android_frontend" (
    echo ✅ Directory created/exists: E:\android_frontend
) else (
    echo ❌ Failed to create directory
    pause
    exit /b 1
)

echo.
echo [2/4] Copying project files...
echo Source: E:\仿随意花小贷源码完整版\android_frontend
echo Target: E:\android_frontend
echo.

xcopy "E:\仿随意花小贷源码完整版\android_frontend\*" "E:\android_frontend\" /E /I /H /Y /Q

if %ERRORLEVEL% equ 0 (
    echo ✅ Project files copied successfully
) else (
    echo ❌ Copy failed with error: %ERRORLEVEL%
    pause
    exit /b 1
)

echo.
echo [3/4] Changing to work directory...
cd /d "E:\android_frontend"

if %ERRORLEVEL% neq 0 (
    echo ❌ Cannot access E:\android_frontend
    pause
    exit /b 1
)

echo ✅ Working directory: %CD%

echo.
echo [4/4] Building APK...
echo This will take a few minutes, please wait...
echo.

gradlew.bat clean
gradlew.bat assembleDebug --stacktrace

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo           🎉 BUILD SUCCESS!
    echo ========================================
    echo.
    
    for /r "app\build\outputs\apk" %%f in (*.apk) do (
        echo 📱 APK Generated: %%f
        
        :: Copy to desktop
        copy "%%f" "%USERPROFILE%\Desktop\SuiYiHua-Latest.apk" >nul 2>&1
        
        if exist "%USERPROFILE%\Desktop\SuiYiHua-Latest.apk" (
            echo ✅ APK copied to Desktop: SuiYiHua-Latest.apk
        )
        
        echo.
        echo 📂 Project Location: E:\android_frontend
        echo 📱 APK Location: %%f
        echo 🖥️ Desktop Copy: %USERPROFILE%\Desktop\SuiYiHua-Latest.apk
        echo.
        echo 🔥 Ready for testing!
        goto :success
    )
    
    echo ❌ APK not found in build output
    
) else (
    echo.
    echo ========================================
    echo           ❌ BUILD FAILED!
    echo ========================================
    echo.
    echo Error Code: %ERRORLEVEL%
    echo.
    echo 💡 Try these solutions:
    echo 1. Run: E:\android_frontend\fix_java.bat
    echo 2. Check Java installation
    echo 3. Check internet connection
)

:success
echo.
echo ========================================
pause
