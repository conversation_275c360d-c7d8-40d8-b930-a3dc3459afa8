@echo off
echo ========================================
echo SuiYiHua APK Builder
echo SDK Path: E:\SDK
echo ========================================
echo.

echo Searching for JDK in E:\SDK\jdks...

set JAVA_FOUND=0

REM Check the jdks directory for available JDK versions
if exist "E:\SDK\jdks" (
    echo Found jdks directory, checking for JDK versions...
    
    REM List available JDK versions
    for /d %%i in ("E:\SDK\jdks\*") do (
        if exist "%%i\bin\java.exe" (
            echo Found JDK: %%i
            set "JAVA_HOME=%%i"
            set JAVA_FOUND=1
            goto setup_env
        )
    )
)

REM If no JDK found in jdks, check cmdline-tools
if exist "E:\SDK\cmdline-tools" (
    echo Checking cmdline-tools for embedded JDK...
    for /d %%i in ("E:\SDK\cmdline-tools\*") do (
        if exist "%%i\bin\java.exe" (
            echo Found JDK in cmdline-tools: %%i
            set "JAVA_HOME=%%i"
            set JAVA_FOUND=1
            goto setup_env
        )
        if exist "%%i\jdk\bin\java.exe" (
            echo Found JDK: %%i\jdk
            set "JAVA_HOME=%%i\jdk"
            set JAVA_FOUND=1
            goto setup_env
        )
    )
)

REM Check Android Studio as fallback
if exist "C:\Program Files\Android\Android Studio\jbr\bin\java.exe" (
    echo Found Android Studio JDK
    set "JAVA_HOME=C:\Program Files\Android\Android Studio\jbr"
    set JAVA_FOUND=1
    goto setup_env
)

if exist "C:\Program Files (x86)\Android\Android Studio\jbr\bin\java.exe" (
    echo Found Android Studio JDK (x86)
    set "JAVA_HOME=C:\Program Files (x86)\Android\Android Studio\jbr"
    set JAVA_FOUND=1
    goto setup_env
)

REM Manual input if nothing found
echo No JDK found automatically.
echo.
echo Please check your E:\SDK\jdks directory and enter the full path.
echo Example: E:\SDK\jdks\openjdk-11.0.2
echo Or: C:\Program Files\Android\Android Studio\jbr
echo.
set /p JAVA_HOME="Enter JDK path: "

if exist "%JAVA_HOME%\bin\java.exe" (
    echo JDK path verified
    set JAVA_FOUND=1
) else (
    echo ERROR: java.exe not found at %JAVA_HOME%\bin\java.exe
    pause
    exit /b 1
)

:setup_env
echo.
echo ========================================
echo Environment Setup
echo ========================================
echo JAVA_HOME = %JAVA_HOME%
echo ANDROID_HOME = E:\SDK
echo.

REM Set environment variables
set "PATH=%JAVA_HOME%\bin;%PATH%"
set "ANDROID_HOME=E:\SDK"
set "ANDROID_SDK_ROOT=E:\SDK"

echo Testing Java...
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java test failed
    pause
    exit /b 1
)

echo Java test passed!
echo.

echo ========================================
echo Building APK
echo ========================================

echo Step 1: Clean build cache
if exist ".gradle" (
    rmdir /s /q ".gradle"
    echo Deleted .gradle cache
)
if exist "app\build" (
    rmdir /s /q "app\build"
    echo Deleted app\build cache
)

echo.
echo Step 2: Gradle clean
gradlew.bat clean
if %errorlevel% neq 0 (
    echo ERROR: Gradle clean failed
    pause
    exit /b 1
)

echo.
echo Step 3: Build debug APK
echo This may take several minutes on first run...
gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ERROR: APK build failed
    echo.
    echo Common solutions:
    echo 1. Check internet connection for dependency downloads
    echo 2. Verify JDK compatibility (Java 8-19 supported)
    echo 3. Try running gradlew.bat assembleDebug --stacktrace for details
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build Complete!
echo ========================================

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo SUCCESS: APK file generated
    
    REM Copy APK with a friendly name
    copy "app\build\outputs\apk\debug\app-debug.apk" "SuiYiHua-Loan-App.apk" >nul
    echo APK copied as: SuiYiHua-Loan-App.apk
    
    REM Show file size
    for %%A in ("SuiYiHua-Loan-App.apk") do (
        set /a size=%%~zA/1024/1024
        echo File size: !size! MB
    )
    
    echo.
    echo File locations:
    echo - Debug APK: app\build\outputs\apk\debug\app-debug.apk
    echo - Ready to use: %CD%\SuiYiHua-Loan-App.apk
    
) else (
    echo WARNING: APK file not found in expected location
    echo Check the app\build\outputs\apk\debug\ directory
)

echo.
echo ========================================
echo Installation Guide
echo ========================================
echo 1. Transfer SuiYiHua-Loan-App.apk to your Android device
echo 2. Enable "Install unknown apps" in Android settings
echo 3. Tap the APK file to install
echo 4. Grant contacts permission when prompted
echo 5. The app will load https://dailuanshej.cn
echo.
echo App Features:
echo - WebView-based interface
echo - Native contacts reading capability
echo - Automatic JavaScript bridge injection
echo - Supports Android 7.0+ devices
echo.

pause
