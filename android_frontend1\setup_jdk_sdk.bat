@echo off
echo.
echo ========================================
echo        Quick JDK Setup for SDK
echo ========================================
echo.

:: Set SDK path
set "SDK_PATH=E:\SDK"

echo Checking your E:\SDK directory for JDK...
echo.

if not exist "%SDK_PATH%" (
    echo ❌ SDK directory not found: %SDK_PATH%
    echo Please make sure Android SDK is installed at E:\SDK
    pause
    exit /b 1
)

if not exist "%SDK_PATH%\jdks" (
    echo ❌ JDK directory not found: %SDK_PATH%\jdks
    echo.
    echo This usually means:
    echo 1. Android Studio is not fully installed
    echo 2. SDK is installed in a different location
    echo.
    echo Please check your Android Studio installation.
    pause
    exit /b 1
)

echo Available JDK versions in your SDK:
echo ====================================
set "JDK_COUNT=0"
set "FIRST_JDK="

for /d %%i in ("%SDK_PATH%\jdks\*") do (
    if exist "%%i\bin\java.exe" (
        set /a JDK_COUNT+=1
        echo %JDK_COUNT%. %%~ni
        if "!FIRST_JDK!"=="" set "FIRST_JDK=%%i"
    )
)

if %JDK_COUNT%==0 (
    echo ❌ No JDK found in %SDK_PATH%\jdks
    echo.
    echo Please install JDK through Android Studio:
    echo 1. Open Android Studio
    echo 2. Go to File > Settings > Appearance ^& Behavior > System Settings > Android SDK
    echo 3. Switch to SDK Tools tab
    echo 4. Check "Android SDK Build-Tools" and apply
    pause
    exit /b 1
)

echo.
if %JDK_COUNT%==1 (
    echo Only one JDK found, setting it automatically...
    set "CHOSEN_JDK=%FIRST_JDK%"
) else (
    echo Multiple JDK versions found.
    echo.
    echo Recommended: Use the first one found (usually the latest)
    set /p "auto_choice=Use the first JDK automatically? (y/n): "
    if /i "!auto_choice!"=="y" (
        set "CHOSEN_JDK=%FIRST_JDK%"
    ) else (
        echo.
        echo Please enter the full path to your preferred JDK:
        set /p "CHOSEN_JDK=JDK Path: "
    )
)

:: Validate chosen JDK
if not exist "%CHOSEN_JDK%\bin\java.exe" (
    echo ❌ Invalid JDK path: %CHOSEN_JDK%
    pause
    exit /b 1
)

echo.
echo ✅ Setting JDK: %CHOSEN_JDK%
echo.

:: Set environment variables (temporary for this session)
set "JAVA_HOME=%CHOSEN_JDK%"
set "PATH=%CHOSEN_JDK%\bin;%PATH%"

:: Test Java
echo Testing Java installation...
"%JAVA_HOME%\bin\java.exe" -version 2>&1

echo.
echo ✅ JDK setup complete!
echo.
echo JAVA_HOME: %JAVA_HOME%
echo.
echo You can now run:
echo   build_final_v2.bat (recommended)
echo   gradlew.bat assembleDebug
echo.

:: Ask if user wants to set permanently
set /p "permanent=Set JAVA_HOME permanently for system? (y/n): "
if /i "%permanent%"=="y" (
    echo Setting JAVA_HOME permanently...
    setx JAVA_HOME "%CHOSEN_JDK%" >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ JAVA_HOME set permanently
    ) else (
        echo ⚠️  Failed to set JAVA_HOME permanently (may need admin rights)
    )
)

echo.
echo ========================================
pause
