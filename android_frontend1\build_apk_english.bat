@echo off
setlocal enabledelayedexpansion

:: Set code page to UTF-8
chcp 65001 >nul

echo ========================================
echo          APK Build Script
echo ========================================
echo.

:: Convert Chinese path to short path
echo [1/5] Converting path format...
for %%i in ("E:\仿随意花小贷源码完整版\android_frontend") do set "SHORT_PATH=%%~si"
echo Short path: %SHORT_PATH%

:: Change to project directory using short path
echo [2/5] Changing to project directory...
cd /d "%SHORT_PATH%"
if %ERRORLEVEL% neq 0 (
    echo ERROR: Cannot access project directory
    echo Please check if the path exists
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

:: Check if gradlew.bat exists
echo [3/5] Checking Gradle wrapper...
if not exist "gradlew.bat" (
    echo ERROR: gradlew.bat not found
    echo Please ensure you are in the correct Android project directory
    pause
    exit /b 1
)
echo Found: gradlew.bat

:: Clean old build
echo.
echo [4/5] Cleaning old build files...
if exist "app\build\outputs\apk" (
    rmdir /s /q "app\build\outputs\apk" 2>nul
    echo Cleaned old APK files
)

:: Build APK
echo.
echo [5/5] Building APK...
echo This may take several minutes...
echo.

gradlew.bat clean assembleDebug

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo           BUILD SUCCESSFUL!
    echo ========================================
    echo.
    
    :: Find and copy APK
    echo Looking for generated APK...
    for /r "app\build\outputs\apk" %%f in (*.apk) do (
        echo.
        echo Found APK: %%f
        
        :: Get file size
        for %%a in ("%%f") do set "size=%%~za"
        echo File size: !size! bytes
        
        :: Copy to desktop with English name
        set "desktop=%USERPROFILE%\Desktop"
        copy "%%f" "!desktop!\SuiYiHua-Latest.apk" >nul 2>&1
        
        if exist "!desktop!\SuiYiHua-Latest.apk" (
            echo.
            echo ✅ APK copied to desktop: SuiYiHua-Latest.apk
            echo.
            echo 📱 Next steps:
            echo 1. Transfer APK to your phone
            echo 2. Install the APK
            echo 3. Test contacts functionality
            echo 4. Check app logs with: view_app_logs.bat
            echo.
        ) else (
            echo ⚠️ Failed to copy APK to desktop
            echo APK location: %%f
        )
        goto :build_success
    )
    
    echo ❌ No APK files found in build output
    echo Please check the build logs above

) else (
    echo.
    echo ========================================
    echo            BUILD FAILED!
    echo ========================================
    echo.
    echo Error code: %ERRORLEVEL%
    echo.
    echo 💡 Common solutions:
    echo 1. Run fix_java.bat to configure JDK
    echo 2. Check internet connection (for dependencies)
    echo 3. Clean project: gradlew.bat clean
    echo 4. Restart your computer and try again
)

:build_success
echo.
echo ========================================
pause
