@echo off
chcp 65001 >nul
echo.
echo ===========================================
echo      Android Studio JDK Configuration
echo ===========================================
echo.

echo Choose your situation:
echo.
echo 1. Fix JDK for Android Studio build
echo 2. Detect Android Studio embedded JDK
echo 3. Set custom JDK path
echo 4. Download and install JDK
echo.
set /p choice="Enter choice (1-4): "

if "%choice%"=="1" goto fix_env
if "%choice%"=="2" goto detect_android_studio_jdk
if "%choice%"=="3" goto temp_java
if "%choice%"=="4" goto download_java
goto end

:detect_android_studio_jdk
echo.
echo Detecting Android Studio embedded JDK...
echo ==========================================

:: Check common Android Studio locations
set "AS_PATHS=C:\Program Files\Android\Android Studio C:\Users\<USER>\AppData\Local\Android\Sdk"

for %%p in (%AS_PATHS%) do (
    if exist "%%p" (
        echo Checking: %%p
        if exist "%%p\jbr" (
            if exist "%%p\jbr\bin\java.exe" (
                echo ✅ Found Android Studio JDK: %%p\jbr
                set "FOUND_JDK=%%p\jbr"
                goto ask_set_as_jdk
            )
        )
        if exist "%%p\jre" (
            if exist "%%p\jre\bin\java.exe" (
                echo ✅ Found Android Studio JRE: %%p\jre
                set "FOUND_JDK=%%p\jre"
                goto ask_set_as_jdk
            )
        )
    )
)

:: Check SDK embedded JDK (Priority location for E:\SDK)
if exist "E:\SDK\jdks" (
    echo.
    echo Checking SDK embedded JDK (Recommended)...
    echo ==========================================
    set "JDK_COUNT=0"
    for /d %%i in ("E:\SDK\jdks\*") do (
        if exist "%%i\bin\java.exe" (
            set /a JDK_COUNT+=1
            echo !JDK_COUNT!. Found SDK JDK: %%~ni
            set "LATEST_JDK=%%i"
        )
    )
    
    if !JDK_COUNT! gtr 0 (
        echo.
        echo ✅ Found !JDK_COUNT! JDK version(s) in SDK
        echo Recommended: !LATEST_JDK!
        set "FOUND_JDK=!LATEST_JDK!"
        goto ask_set_as_jdk
    )
)

echo ❌ No Android Studio JDK found automatically
echo.
echo Please check:
echo 1. Android Studio is properly installed
echo 2. JDK is configured in Android Studio settings
echo 3. SDK path is correct (E:\SDK)
goto end

:ask_set_as_jdk
echo.
echo Found JDK: %FOUND_JDK%
echo Testing JDK...
"%FOUND_JDK%\bin\java.exe" -version 2>&1
echo.
set /p "use_found=Use this JDK for builds? (y/n): "
if /i "%use_found%"=="y" (
    echo Setting JAVA_HOME...
    setx JAVA_HOME "%FOUND_JDK%" >nul
    echo ✅ JAVA_HOME set to: %FOUND_JDK%
    echo.
    echo For Android Studio:
    echo 1. Open Android Studio
    echo 2. Go to File ^> Project Structure
    echo 3. Set JDK location to: %FOUND_JDK%
    echo 4. Sync project
) else (
    echo JDK not set. You can manually configure it.
)
goto end

:fix_env
echo.
echo Setting up JDK for Android Studio...
echo ===================================
echo.
echo Common JDK paths for Android Studio:
echo 1. E:\SDK\jdks\openjdk-11
echo 2. E:\SDK\jdks\openjdk-17
echo 3. C:\Program Files\Java\jdk-11.x.x
echo 4. Android Studio embedded JDK
echo.
set /p java_path="Enter JDK path: "

if exist "%java_path%\bin\java.exe" (
    echo ✅ Found JDK: %java_path%
    echo.
    echo Testing JDK version...
    "%java_path%\bin\java.exe" -version 2>&1
    echo.
    echo Setting environment variables...
    setx JAVA_HOME "%java_path%" >nul
    echo ✅ JAVA_HOME set successfully
    echo.
    echo For Android Studio:
    echo 1. Restart Android Studio
    echo 2. Go to File ^> Project Structure ^> SDK Location
    echo 3. Set JDK location to: %java_path%
    echo 4. Click Apply and sync project
    echo.
    echo You can now build the project in Android Studio
) else (
    echo ❌ JDK not found at: %java_path%\bin\java.exe
    echo Please check the path and try again
)
goto end

:download_java
echo.
echo Download JDK for Android Studio development:
echo ==========================================
echo.
echo Recommended JDK versions:
echo 1. OpenJDK 11 (LTS): https://adoptium.net/temurin/releases/?version=11
echo 2. OpenJDK 17 (LTS): https://adoptium.net/temurin/releases/?version=17
echo 3. Oracle JDK: https://www.oracle.com/java/technologies/downloads/
echo.
echo Installation tips:
echo - Install to default location (C:\Program Files\Java\)
echo - Or use Android Studio's embedded JDK from SDK
echo.
echo After installation, run this script again and choose option 1
goto end

:temp_java
echo.
echo Temporary JDK setup for current session:
echo =======================================
set /p temp_java_path="Enter JDK path: "

if exist "%temp_java_path%\bin\java.exe" (
    echo ✅ Setting temporary JDK path
    set JAVA_HOME=%temp_java_path%
    set PATH=%temp_java_path%\bin;%PATH%
    echo.
    echo Temporary environment set:
    echo JAVA_HOME: %temp_java_path%
    echo.
    echo You can now:
    echo 1. Open Android Studio and build the project
    echo 2. Or run: gradlew.bat assembleDebug
    echo.
    echo Note: This is temporary - restart your session to revert
) else (
    echo ❌ Invalid JDK path: %temp_java_path%
    echo Please check the path and try again
)
goto end

:end
pause
