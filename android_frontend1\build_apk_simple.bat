@echo off
echo ========================================
echo 随意花APP - 快速构建工具
echo ========================================
echo.

REM 尝试常见的Android Studio JDK路径
set JAVA_FOUND=0

echo 正在搜索Java环境...

REM 路径1: Program Files
if exist "C:\Program Files\Android\Android Studio\jbr\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Android\Android Studio\jbr"
    set JAVA_FOUND=1
    echo [找到] Java: %JAVA_HOME%
    goto setup_env
)

REM 路径2: Program Files (x86)
if exist "C:\Program Files (x86)\Android\Android Studio\jbr\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files (x86)\Android\Android Studio\jbr"
    set JAVA_FOUND=1
    echo [找到] Java: %JAVA_HOME%
    goto setup_env
)

REM 路径3: 用户手动输入
echo [未找到] 自动搜索失败
echo.
echo 请输入您的Android Studio JDK路径：
echo 例如: C:\Program Files\Android\Android Studio\jbr
set /p JAVA_HOME="JDK路径: "

if exist "%JAVA_HOME%\bin\java.exe" (
    set JAVA_FOUND=1
    echo [确认] Java路径有效
) else (
    echo [错误] 无效的Java路径
    pause
    exit /b 1
)

:setup_env
echo.
echo 设置Java环境变量...
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo 测试Java版本...
java -version
if %errorlevel% neq 0 (
    echo [错误] Java无法运行
    pause
    exit /b 1
)

echo.
echo ========================================
echo 开始构建APK
echo ========================================

echo 第1步: 清理项目
gradlew.bat clean
if %errorlevel% neq 0 (
    echo [错误] 清理失败
    pause
    exit /b 1
)

echo.
echo 第2步: 构建调试版APK
gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo [错误] 构建失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建成功！
echo ========================================

REM 复制APK文件
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    copy "app\build\outputs\apk\debug\app-debug.apk" "随意花APP.apk" >nul
    echo APK文件: 随意花APP.apk
    
    REM 显示文件大小
    for %%A in ("随意花APP.apk") do (
        set /a size=%%~zA/1024/1024
        echo 文件大小: !size! MB
    )
) else (
    echo [警告] 未找到生成的APK文件
)

echo.
echo 安装说明：
echo 1. 将APK传输到Android设备
echo 2. 启用"未知来源"安装权限
echo 3. 点击APK文件安装
echo 4. 首次启动时授予通讯录权限

echo.
pause
