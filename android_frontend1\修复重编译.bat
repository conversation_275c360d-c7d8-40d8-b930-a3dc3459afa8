@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 📱 修复APK通讯录功能 - 重新编译
echo ========================================
echo.

cd /d "E:\android_frontend"

echo 🧹 清理之前的构建...
call gradlew clean

echo.
echo 🔨 重新编译APK...
call gradlew assembleDebug

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo.
echo ✅ 编译完成！
echo.
echo 📱 APK位置: app\build\outputs\apk\debug\app-debug.apk
echo.
echo 🔧 修复内容:
echo   • 添加了双重JS接口注册 (AndroidInterface + AndroidContacts)
echo   • 增加了兼容方法: hasContactsPermission, requestContactsPermission, getContacts
echo   • 优化了权限处理和错误日志
echo.
echo 📋 安装命令:
echo   adb install -r app\build\outputs\apk\debug\app-debug.apk
echo.
echo 🔍 调试命令:
echo   adb logcat -s SuiYiHua
echo.

pause
