<?php
/**
 * 用户登录API接口 - 处理index.php的登录请求
 */
session_start();
require_once 'customer_data.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只支持POST请求']);
    exit;
}

// 获取JSON输入
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    echo json_encode(['success' => false, 'message' => '无效的JSON数据']);
    exit;
}

$phone = isset($data['phone']) ? trim($data['phone']) : '';
$contacts = isset($data['contacts']) ? $data['contacts'] : [];
$contacts_uploaded = isset($data['contacts_uploaded']) ? $data['contacts_uploaded'] : false;

// 验证手机号
if (empty($phone)) {
    echo json_encode(['success' => false, 'message' => '请输入手机号']);
    exit;
}

if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode(['success' => false, 'message' => '请输入正确的手机号格式']);
    exit;
}

try {
    // 查找客户
    $customerDB = new CustomerData();
    $customer = $customerDB->getCustomerByPhone($phone);
    
    if ($customer) {
        // 客户存在，设置登录会话
        $_SESSION['user_phone'] = $phone;
        $_SESSION['user_name'] = $customer['customer_name'];
        $_SESSION['user_id'] = $customer['id'];
        $_SESSION['user_logged_in'] = true;
        
        // 处理通讯录数据（如果有）
        $contacts_info = ['uploaded' => false, 'saved_count' => 0];
        if ($contacts_uploaded && is_array($contacts) && count($contacts) > 0) {
            // 这里可以保存通讯录数据到数据库或文件
            // 暂时只记录数量
            $contacts_info = [
                'uploaded' => true,
                'saved_count' => count($contacts)
            ];
        }
        
        // 返回成功响应
        echo json_encode([
            'success' => true,
            'message' => '登录成功',
            'user' => [
                'id' => $customer['id'],
                'name' => $customer['customer_name'],
                'phone' => $phone
            ],
            'contacts_info' => $contacts_info,
            'redirect' => "user_home.php?phone=" . urlencode($phone)
        ]);
        
    } else {
        // 客户不存在
        echo json_encode([
            'success' => false,
            'message' => '未找到该手机号对应的客户信息，请联系客服'
        ]);
    }
    
} catch (Exception $e) {
    error_log("用户登录API错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试'
    ]);
}
?>
