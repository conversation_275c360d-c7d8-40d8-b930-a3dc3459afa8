@echo off
echo ========================================
echo 🏷️ 构建优易花品牌统一版APK
echo ========================================

echo.
echo 📋 品牌统一特性：
echo   ✅ 应用名称: 优易花
echo   ✅ 日志标签: YouYiHua
echo   ✅ 用户代理: YouYiHuaApp/1.0
echo   ✅ JavaScript接口: 优易花APP
echo   ✅ 打印标题: 优易花借款合同
echo   ✅ 其他功能完全保持不变
echo.

echo 🔧 步骤1: 验证品牌名称修改...
echo 检查应用名称...

findstr /C:"优易花" "app\src\main\res\values\strings.xml" >nul
if %errorlevel% equ 0 (
    echo ✅ 应用名称已设置为优易花
) else (
    echo ❌ 应用名称未正确设置
    pause
    exit /b 1
)

echo 检查日志标签...
findstr /C:"YouYiH<PERSON>" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ 日志标签已更新为YouYiHua
) else (
    echo ❌ 日志标签未正确更新
    pause
    exit /b 1
)

echo 检查用户代理...
findstr /C:"YouYiHuaApp" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ 用户代理已更新为YouYiHuaApp
) else (
    echo ❌ 用户代理未正确更新
    pause
    exit /b 1
)

echo 检查JavaScript接口...
findstr /C:"优易花APP" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ JavaScript接口已更新为优易花APP
) else (
    echo ❌ JavaScript接口未正确更新
    pause
    exit /b 1
)

echo.
echo 🔧 步骤2: 清理构建环境...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 已清理构建缓存
)

if exist "build" (
    rmdir /s /q "build"
    echo ✅ 已清理项目缓存
)

echo.
echo 🔧 步骤3: 构建优易花品牌版APK...

call .\gradlew clean
if %errorlevel% neq 0 (
    echo ⚠️ Clean有警告，继续构建...
)

echo 正在构建优易花品牌统一版APK...
call .\gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    echo.
    echo 💡 请检查：
    echo   1. Java环境配置
    echo   2. Android SDK路径
    echo   3. 网络连接状态
    echo   4. 磁盘空间
    echo.
    pause
    exit /b 1
)

echo.
echo 📦 步骤4: 生成优易花品牌版APK...
set APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk
set APK_DEST=youyihua_brand.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%"
    echo ✅ 优易花品牌版APK已生成: %APK_DEST%
    
    REM 显示文件信息
    for %%A in ("%APK_DEST%") do (
        set APK_SIZE=%%~zA
        set APK_DATE=%%~tA
    )
    echo   文件大小: %APK_SIZE% 字节
    echo   创建时间: %APK_DATE%
    
) else (
    echo ❌ 找不到构建的APK文件
    pause
    exit /b 1
)

echo.
echo 📤 步骤5: 部署APK...
if exist "..\源码\apk\" (
    copy "%APK_DEST%" "..\源码\apk\%APK_DEST%"
    echo ✅ APK已部署到服务器目录
)

echo.
echo 🧹 步骤6: 清理旧版本...
if exist "youyihua_clean.apk" (
    del "youyihua_clean.apk"
    echo ✅ 已删除旧版本APK
)

echo.
echo ========================================
echo 🎉 优易花品牌统一版APK构建完成！
echo ========================================
echo.
echo 🏷️ 品牌统一确认：
echo   📱 应用名称: 优易花
echo   🏷️ 应用描述: 优易花小贷助手
echo   📊 日志标签: YouYiHua (便于调试)
echo   🌐 用户代理: YouYiHuaApp/1.0 (服务器识别)
echo   💬 JavaScript: 优易花APP接口
echo   🖨️ 打印标题: 优易花借款合同
echo.
echo 🔄 保持不变的功能：
echo   ✅ 网站URL: https://dailuanshej.cn
echo   ✅ 包名: com.dailuanshej.loan
echo   ✅ 合同功能: 完全正常
echo   ✅ 印章显示: 优化版本
echo   ✅ 打印功能: 完全支持
echo   ✅ 通讯录功能: 完全支持
echo   ✅ 移动端优化: 完全保留
echo.
echo 🧪 安装测试：
echo   1. 完全卸载之前的版本
echo   2. 安装优易花品牌版: %APK_DEST%
echo   3. 打开应用 → 显示"优易花"应用名
echo   4. 正常登录和使用所有功能
echo   5. 验证品牌名称统一显示
echo.
echo 📋 品牌检查项：
echo   • 应用图标下显示"优易花"
echo   • 应用内所有提示都是"优易花"
echo   • 打印合同标题显示"优易花借款合同"
echo   • 调试日志显示"YouYiHua"标签
echo.
pause
