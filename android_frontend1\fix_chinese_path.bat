@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul

echo ========================================
echo        Path Converter Tool
echo ========================================
echo.

echo Testing path conversion...
echo.

:: Original path with Chinese characters
set "ORIGINAL_PATH=E:\仿随意花小贷源码完整版\android_frontend"
echo Original path: %ORIGINAL_PATH%

:: Convert to short path (8.3 format)
for %%i in ("%ORIGINAL_PATH%") do set "SHORT_PATH=%%~si"
echo Short path: %SHORT_PATH%

:: Test if paths are accessible
echo.
echo Testing path accessibility:
echo.

if exist "%ORIGINAL_PATH%" (
    echo ✅ Original path exists
) else (
    echo ❌ Original path not found
)

if exist "%SHORT_PATH%" (
    echo ✅ Short path exists
    
    :: Test changing directory
    pushd "%SHORT_PATH%" 2>nul
    if !ERRORLEVEL! equ 0 (
        echo ✅ Can change to short path directory
        echo Current directory: !CD!
        popd
    ) else (
        echo ❌ Cannot change to short path directory
    )
) else (
    echo ❌ Short path not found
)

echo.
echo ========================================
echo         Recommended Solution
echo ========================================
echo.
echo To avoid Chinese path issues:
echo.
echo 1. Use short path format: %SHORT_PATH%
echo 2. Or move project to English path like: C:\Android\SuiYiHua
echo 3. Or create symbolic link with English name
echo.
echo Creating symbolic link example:
echo mklink /D "C:\SuiYiHua" "%ORIGINAL_PATH%"
echo.

echo Do you want to try building APK with short path? (y/n)
set /p choice="> "

if /i "%choice%"=="y" (
    echo.
    echo Starting build with short path...
    call "%~dp0build_apk_english.bat"
) else (
    echo.
    echo You can manually run: build_apk_english.bat
)

pause
