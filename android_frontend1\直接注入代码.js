// 直接注入到 https://dailuanshej.cn 的简化版通讯录测试代码
// 复制此代码到浏览器控制台或直接嵌入页面

(function() {
    'use strict';
    
    // 创建测试按钮和显示区域
    function createTestUI() {
        // 移除已存在的测试UI
        const existingUI = document.getElementById('contacts-test-ui');
        if (existingUI) {
            existingUI.remove();
        }
        
        // 创建容器
        const container = document.createElement('div');
        container.id = 'contacts-test-ui';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 320px;
            max-height: 400px;
            background: #fff;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            font-family: Arial, sans-serif;
            overflow: hidden;
        `;
        
        // 创建标题栏
        const header = document.createElement('div');
        header.style.cssText = `
            background: #007bff;
            color: white;
            padding: 10px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;
        header.innerHTML = `
            <span>📱 通讯录功能测试</span>
            <button onclick="document.getElementById('contacts-test-ui').remove()" 
                    style="background: none; border: none; color: white; cursor: pointer; font-size: 18px;">×</button>
        `;
        
        // 创建内容区域
        const content = document.createElement('div');
        content.style.cssText = `
            padding: 15px;
            max-height: 320px;
            overflow-y: auto;
        `;
        
        // 创建测试按钮
        const testButton = document.createElement('button');
        testButton.textContent = '🔍 获取通讯录';
        testButton.style.cssText = `
            width: 100%;
            padding: 12px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 15px;
        `;
        testButton.onclick = testContacts;
        
        // 创建状态显示
        const status = document.createElement('div');
        status.id = 'test-status';
        status.style.cssText = `
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 10px;
            min-height: 20px;
            border-left: 4px solid #6c757d;
        `;
        status.innerHTML = '等待测试...';
        
        // 创建结果显示
        const results = document.createElement('div');
        results.id = 'test-results';
        results.style.cssText = `
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        `;
        
        // 组装UI
        content.appendChild(testButton);
        content.appendChild(status);
        content.appendChild(results);
        container.appendChild(header);
        container.appendChild(content);
        
        // 添加到页面
        document.body.appendChild(container);
        
        console.log('✅ 通讯录测试UI已创建');
    }
    
    // 更新状态显示
    function updateStatus(message, type = 'info') {
        const status = document.getElementById('test-status');
        if (!status) return;
        
        const colors = {
            'info': '#6c757d',
            'success': '#28a745',
            'warning': '#ffc107',
            'error': '#dc3545'
        };
        
        status.style.borderLeftColor = colors[type] || colors.info;
        status.innerHTML = message;
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    // 更新结果显示
    function updateResults(content) {
        const results = document.getElementById('test-results');
        if (!results) return;
        
        results.innerHTML = content;
    }
    
    // 环境检测
    function detectEnvironment() {
        const ua = navigator.userAgent;
        const isAndroid = /Android/i.test(ua);
        const isWebView = /wv|WebView/i.test(ua);
        const hasAndroidInterface = typeof window.AndroidInterface !== 'undefined';
        
        return {
            isAndroid,
            isWebView,
            hasAndroidInterface,
            userAgent: ua
        };
    }
    
    // 主测试函数
    function testContacts() {
        updateStatus('🔍 开始检测环境...', 'info');
        updateResults('');
        
        const env = detectEnvironment();
        
        let envInfo = `
            <div style="margin-bottom: 10px;">
                <strong>📱 环境信息：</strong><br>
                • Android设备: ${env.isAndroid ? '✅' : '❌'}<br>
                • WebView环境: ${env.isWebView ? '✅' : '❌'}<br>
                • Android接口: ${env.hasAndroidInterface ? '✅' : '❌'}<br>
                • User Agent: <small>${env.userAgent}</small>
            </div>
        `;
        
        updateResults(envInfo);
        
        // 检查Android接口
        if (!env.hasAndroidInterface) {
            updateStatus('⚠️ 未检测到AndroidInterface，可能不在APP环境中', 'warning');
            updateResults(envInfo + `
                <div style="color: #ffc107; margin-top: 10px;">
                    <strong>⚠️ 警告：</strong><br>
                    未检测到Android接口，请确认：<br>
                    1. 当前是否在APP中打开此页面<br>
                    2. APP是否正确注入了AndroidInterface<br>
                    3. 是否使用最新版本的APP
                </div>
            `);
            return;
        }
        
        // 测试权限检查
        updateStatus('🔐 检查通讯录权限...', 'info');
        
        try {
            if (typeof window.AndroidInterface.hasContactsPermission === 'function') {
                const hasPermission = window.AndroidInterface.hasContactsPermission();
                
                if (hasPermission) {
                    updateStatus('✅ 已有通讯录权限，开始读取...', 'success');
                    readContacts();
                } else {
                    updateStatus('📋 需要申请通讯录权限...', 'warning');
                    requestPermission();
                }
            } else {
                updateStatus('⚠️ 权限检查方法不可用，尝试直接读取...', 'warning');
                readContacts();
            }
        } catch (error) {
            updateStatus('❌ 权限检查失败: ' + error.message, 'error');
            updateResults(envInfo + `
                <div style="color: #dc3545; margin-top: 10px;">
                    <strong>❌ 错误：</strong><br>
                    ${error.message}<br>
                    <small>Stack: ${error.stack}</small>
                </div>
            `);
        }
    }
    
    // 申请权限
    function requestPermission() {
        try {
            if (typeof window.AndroidInterface.requestContactsPermission === 'function') {
                window.AndroidInterface.requestContactsPermission();
                updateStatus('📱 权限申请已发送，请在弹窗中授权...', 'info');
                
                // 设置权限回调监听
                window.onContactsPermissionResult = function(granted) {
                    if (granted) {
                        updateStatus('✅ 权限已授权！开始读取通讯录...', 'success');
                        setTimeout(readContacts, 1000);
                    } else {
                        updateStatus('❌ 权限被拒绝', 'error');
                        updateResults(document.getElementById('test-results').innerHTML + `
                            <div style="color: #dc3545; margin-top: 10px;">
                                <strong>❌ 权限被拒绝</strong><br>
                                请在系统设置中手动授权通讯录权限
                            </div>
                        `);
                    }
                };
            } else {
                updateStatus('❌ 权限申请方法不可用', 'error');
            }
        } catch (error) {
            updateStatus('❌ 权限申请失败: ' + error.message, 'error');
        }
    }
    
    // 读取通讯录
    function readContacts() {
        try {
            if (typeof window.AndroidInterface.getContacts === 'function') {
                updateStatus('📖 正在读取通讯录数据...', 'info');
                
                const contactsData = window.AndroidInterface.getContacts();
                
                if (contactsData) {
                    try {
                        const contacts = JSON.parse(contactsData);
                        displayContacts(contacts);
                    } catch (parseError) {
                        updateStatus('❌ 通讯录数据解析失败', 'error');
                        updateResults(document.getElementById('test-results').innerHTML + `
                            <div style="color: #dc3545; margin-top: 10px;">
                                <strong>❌ 数据解析错误：</strong><br>
                                ${parseError.message}<br>
                                <strong>原始数据：</strong><br>
                                <pre style="font-size: 10px; background: #f8f9fa; padding: 5px; overflow: auto; max-height: 100px;">${contactsData}</pre>
                            </div>
                        `);
                    }
                } else {
                    updateStatus('⚠️ 未获取到通讯录数据', 'warning');
                }
            } else {
                updateStatus('❌ 通讯录读取方法不可用', 'error');
            }
        } catch (error) {
            updateStatus('❌ 读取失败: ' + error.message, 'error');
        }
    }
    
    // 显示通讯录数据
    function displayContacts(contacts) {
        if (!contacts || !Array.isArray(contacts)) {
            updateStatus('⚠️ 通讯录数据格式错误', 'warning');
            return;
        }
        
        if (contacts.length === 0) {
            updateStatus('📱 通讯录为空', 'info');
            updateResults(document.getElementById('test-results').innerHTML + `
                <div style="color: #6c757d; margin-top: 10px;">
                    📱 通讯录中没有联系人
                </div>
            `);
            return;
        }
        
        updateStatus(`✅ 成功读取 ${contacts.length} 个联系人`, 'success');
        
        let contactsHtml = `
            <div style="margin-top: 15px;">
                <strong>📞 通讯录数据 (${contacts.length}个联系人)：</strong>
                <div style="max-height: 150px; overflow-y: auto; margin-top: 5px;">
        `;
        
        contacts.slice(0, 10).forEach((contact, index) => {
            contactsHtml += `
                <div style="padding: 5px; border-bottom: 1px solid #eee; font-size: 11px;">
                    <strong>${contact.name || '未知姓名'}</strong><br>
                    <span style="color: #666;">${contact.phone || '无电话号码'}</span>
                </div>
            `;
        });
        
        if (contacts.length > 10) {
            contactsHtml += `
                <div style="padding: 5px; color: #666; font-style: italic; text-align: center;">
                    ... 还有 ${contacts.length - 10} 个联系人
                </div>
            `;
        }
        
        contactsHtml += `
                </div>
                <div style="margin-top: 10px; padding: 8px; background: #d4edda; border-radius: 4px; font-size: 12px; color: #155724;">
                    ✅ 通讯录功能正常工作！
                </div>
            </div>
        `;
        
        updateResults(document.getElementById('test-results').innerHTML + contactsHtml);
    }
    
    // 初始化
    function init() {
        console.log('🚀 初始化通讯录测试工具...');
        createTestUI();
        updateStatus('📱 测试工具已就绪，点击按钮开始测试', 'info');
    }
    
    // 如果页面已加载完成，直接初始化；否则等待加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();

// 使用说明：
// 1. 在APP中打开 https://dailuanshej.cn
// 2. 按F12打开开发者工具（如果支持）
// 3. 复制粘贴此代码到控制台并回车
// 4. 或者直接在页面中嵌入此脚本
// 5. 点击"获取通讯录"按钮测试功能
