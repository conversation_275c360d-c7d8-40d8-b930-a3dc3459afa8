<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 信任的域名配置 - 防止被识别为恶意网络行为 -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">dailuanshej.cn</domain>
        <domain includeSubdomains="true">www.dailuanshej.cn</domain>
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>

        <!-- 证书固定 - 增强安全性 -->
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>

    <!-- 基础安全配置 - 防止被标记为不安全应用 -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- 信任系统证书 -->
            <certificates src="system"/>
            <!-- 信任用户添加的证书 -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>

    <!-- 调试配置 - 仅在debug模式下生效 -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>
