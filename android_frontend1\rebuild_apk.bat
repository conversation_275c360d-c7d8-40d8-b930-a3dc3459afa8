@echo off
chcp 65001
echo ========================================
echo          重新生成随意花APK
echo ========================================
echo.

echo [1/3] 切换到项目目录...
cd /d "E:\仿随意花小贷源码完整版\android_frontend"
echo 当前目录: %CD%

echo.
echo [2/3] 清理旧构建文件...
if exist "app\build\outputs\apk" (
    rmdir /s /q "app\build\outputs\apk"
    echo 清理完成
)

echo.
echo [3/3] 开始编译APK...
echo 请等待，编译需要几分钟时间...
echo.

gradlew.bat clean
gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo               编译成功！
    echo ========================================
    echo.
    
    echo 查找APK文件...
    for /r "app\build\outputs\apk" %%i in (*.apk) do (
        echo.
        echo APK文件位置: %%i
        echo 文件大小: 
        dir "%%i" | find ".apk"
        
        echo.
        echo 复制APK到桌面...
        copy "%%i" "%USERPROFILE%\Desktop\随意花-最新版.apk"
        
        if exist "%USERPROFILE%\Desktop\随意花-最新版.apk" (
            echo ✅ APK已复制到桌面: 随意花-最新版.apk
            echo.
            echo 🎯 下一步：
            echo 1. 将APK传输到手机
            echo 2. 安装并测试通讯录功能
            echo 3. 访问测试页面验证功能
        ) else (
            echo ⚠️ 复制失败，请手动复制APK
        )
        goto :success
    )
    
    echo ❌ 未找到APK文件，请检查编译日志
    
) else (
    echo.
    echo ========================================
    echo               编译失败！
    echo ========================================
    echo.
    echo 错误代码: %ERRORLEVEL%
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查Java环境变量
    echo 2. 运行 fix_java.bat 修复JDK配置
    echo 3. 检查网络连接（下载依赖）
    echo 4. 清理项目重新编译
)

:success
echo.
pause
