{"logs": [{"outputFile": "com.dailuanshej.loan.app-mergeDebugResources-27:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "E:\\SDK\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "7752", "endColumns": "100", "endOffsets": "7848"}}, {"source": "E:\\SDK\\caches\\transforms-3\\ae146d59d58c77d62f24ab479ef1acb9\\transformed\\appcompat-1.5.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,7666", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,7747"}}, {"source": "E:\\SDK\\caches\\transforms-3\\7cfde09bef56db81a739e41054cc6284\\transformed\\material-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,357,458,578,659,723,815,894,954,1044,1115,1178,1253,1317,1371,1498,1556,1618,1672,1751,1892,1979,2061,2200,2283,2367,2454,2510,2561,2627,2701,2781,2868,2941,3018,3087,3161,3249,3326,3419,3515,3589,3669,3766,3818,3884,3971,4059,4121,4185,4248,4360,4469,4576,4686,4746,4801", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,83,100,119,80,63,91,78,59,89,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,86,55,50,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,59,54,76", "endOffsets": "268,352,453,573,654,718,810,889,949,1039,1110,1173,1248,1312,1366,1493,1551,1613,1667,1746,1887,1974,2056,2195,2278,2362,2449,2505,2556,2622,2696,2776,2863,2936,3013,3082,3156,3244,3321,3414,3510,3584,3664,3761,3813,3879,3966,4054,4116,4180,4243,4355,4464,4571,4681,4741,4796,4873"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3145,3246,3366,3447,3511,3603,3682,3742,3832,3903,3966,4041,4105,4159,4286,4344,4406,4460,4539,4680,4767,4849,4988,5071,5155,5242,5298,5349,5415,5489,5569,5656,5729,5806,5875,5949,6037,6114,6207,6303,6377,6457,6554,6606,6672,6759,6847,6909,6973,7036,7148,7257,7364,7474,7534,7589", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "endColumns": "12,83,100,119,80,63,91,78,59,89,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,86,55,50,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,59,54,76", "endOffsets": "318,3140,3241,3361,3442,3506,3598,3677,3737,3827,3898,3961,4036,4100,4154,4281,4339,4401,4455,4534,4675,4762,4844,4983,5066,5150,5237,5293,5344,5410,5484,5564,5651,5724,5801,5870,5944,6032,6109,6202,6298,6372,6452,6549,6601,6667,6754,6842,6904,6968,7031,7143,7252,7359,7469,7529,7584,7661"}}]}]}