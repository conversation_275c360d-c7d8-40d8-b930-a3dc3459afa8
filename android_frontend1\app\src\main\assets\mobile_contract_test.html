<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>移动端合同布局测试</title>
    <style>
        body {
            margin: 0;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            line-height: 1.5;
            color: #333;
        }

        /* 主容器 - 移动端优化 */
        .container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            background: white;
            padding: 15px;
            position: relative;
            box-sizing: border-box;
        }

        /* 合同头部 */
        .contract-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #333;
        }

        .contract-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .contract-no {
            font-size: 12px;
            color: #666;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }

        /* 章节样式 */
        .section {
            margin-bottom: 15px;
            background: white;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            background: #f5f5f5;
            padding: 8px;
            margin: -12px -12px 10px -12px;
            border-radius: 6px 6px 0 0;
        }

        /* 移动端信息卡片布局 */
        .info-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .info-row {
            display: flex;
            flex-direction: column;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .info-value {
            color: #333;
            font-size: 16px;
            word-wrap: break-word;
            word-break: break-all;
        }

        /* 测试按钮 */
        .test-buttons {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .test-btn {
            display: block;
            margin-bottom: 5px;
            padding: 8px 12px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-buttons">
        <a href="customer_contract_mobile.php?id=1" class="test-btn">移动端合同</a>
        <a href="customer_contract_mobile_h5.php?id=1" class="test-btn">H5优化版</a>
        <a href="customer_contract.php?id=1" class="test-btn">桌面版合同</a>
    </div>

    <div class="container">
        <!-- 合同头部 -->
        <div class="contract-header">
            <div class="contract-title">
                小额贷款借款合同
                <span style="color: #dc3545; font-weight: bold; margin-left: 10px; font-size: 18px;">逾期</span>
            </div>
            <div class="contract-no">合同编号：XD202507130001</div>
        </div>

        <!-- 甲方信息 -->
        <div class="section">
            <div class="section-title">甲方（借款人）信息</div>
            <div class="info-card">
                <div class="info-row">
                    <div class="info-label">姓名</div>
                    <div class="info-value">张三</div>
                </div>
                <div class="info-row">
                    <div class="info-label">身份证号</div>
                    <div class="info-value">110101199001011234</div>
                </div>
                <div class="info-row">
                    <div class="info-label">联系电话</div>
                    <div class="info-value">13800138001</div>
                </div>
                <div class="info-row">
                    <div class="info-label">联系地址</div>
                    <div class="info-value">北京市朝阳区测试地址</div>
                </div>
                <div class="info-row">
                    <div class="info-label">银行卡号</div>
                    <div class="info-value">6222021234567890123</div>
                </div>
                <div class="info-row">
                    <div class="info-label">开户银行</div>
                    <div class="info-value">中国工商银行</div>
                </div>
            </div>
        </div>

        <!-- 乙方信息 -->
        <div class="section">
            <div class="section-title">乙方（贷款人）信息</div>
            <div class="info-card">
                <div class="info-row">
                    <div class="info-label">公司名称</div>
                    <div class="info-value">随意花金融有限公司</div>
                </div>
                <div class="info-row">
                    <div class="info-label">联系电话</div>
                    <div class="info-value">400-888-8888</div>
                </div>
                <div class="info-row">
                    <div class="info-label">公司地址</div>
                    <div class="info-value">北京市朝阳区金融街123号</div>
                </div>
                <div class="info-row">
                    <div class="info-label">负责人</div>
                    <div class="info-value">张经理</div>
                </div>
            </div>
        </div>

        <!-- 借款详情 -->
        <div class="section">
            <div class="section-title">借款详情</div>
            <div class="info-card">
                <div class="info-row">
                    <div class="info-label">借款金额</div>
                    <div class="info-value">人民币 <strong>5,000.00</strong> 元</div>
                </div>
                <div class="info-row">
                    <div class="info-label">借款期限</div>
                    <div class="info-value">12 个月</div>
                </div>
                <div class="info-row">
                    <div class="info-label">月利率</div>
                    <div class="info-value">2%</div>
                </div>
                <div class="info-row">
                    <div class="info-label">总利息</div>
                    <div class="info-value">人民币 1,200.00 元</div>
                </div>
                <div class="info-row">
                    <div class="info-label">月还款金额</div>
                    <div class="info-value">人民币 516.67 元</div>
                </div>
                <div class="info-row">
                    <div class="info-label">总还款金额</div>
                    <div class="info-value">人民币 6,200.00 元</div>
                </div>
                <div class="info-row">
                    <div class="info-label">借款状态</div>
                    <div class="info-value">
                        <span style="color: #dc3545; font-weight: bold;">逾期</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示当前屏幕信息
        function showScreenInfo() {
            const info = `
屏幕宽度: ${window.innerWidth}px
屏幕高度: ${window.innerHeight}px
设备像素比: ${window.devicePixelRatio}
用户代理: ${navigator.userAgent}
            `;
            console.log('📱 设备信息:', info);
        }

        // 页面加载完成后显示信息
        window.addEventListener('load', showScreenInfo);
        window.addEventListener('resize', showScreenInfo);

        // 测试文字方向
        console.log('📝 文字方向测试: 这是一段测试文字，应该水平显示');
    </script>
</body>
</html>
