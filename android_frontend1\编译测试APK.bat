@echo off
chcp 65001 >nul
echo ========================================
echo    权限调试APK编译和安装脚本
echo ========================================
echo.

cd /d "E:\仿随意花小贷源码完整版\android_frontend"

echo [1/5] 检查项目结构...
if not exist "gradlew.bat" (
    echo ❌ 错误：找不到gradlew.bat文件
    pause
    exit /b 1
)

if not exist "app\src\main\java\com\dailuanshej\loan\MainActivity.java" (
    echo ❌ 错误：找不到MainActivity.java文件
    pause
    exit /b 1
)

echo ✅ 项目结构正常

echo.
echo [2/5] 准备调试文件...
if exist "权限调试测试页面.html" (
    copy "权限调试测试页面.html" "app\src\main\assets\debug_test.html" >nul 2>&1
    echo ✅ 调试页面已复制到assets
) else (
    echo ⚠️  调试页面不存在，使用默认页面
)

echo.
echo [3/5] 清理旧的构建文件...
call gradlew.bat clean
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 清理失败
    pause
    exit /b 1
)
echo ✅ 清理完成

echo.
echo [3/5] 开始编译APK...
call gradlew.bat assembleDebug
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败，请检查错误信息
    pause
    exit /b 1
)
echo ✅ 编译成功

echo.
echo [4/5] 检查APK文件...
set APK_PATH=app\build\outputs\apk\debug\app-debug.apk
if not exist "%APK_PATH%" (
    echo ❌ 错误：找不到生成的APK文件
    echo 预期路径：%APK_PATH%
    pause
    exit /b 1
)

for %%A in ("%APK_PATH%") do set APK_SIZE=%%~zA
echo ✅ APK生成成功
echo    文件大小：%APK_SIZE% 字节
echo    路径：%CD%\%APK_PATH%

echo.
echo [5/5] 检查设备连接并安装...
adb devices > devices.txt 2>&1
findstr /C:"device" devices.txt | findstr /V /C:"List of devices" > nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  警告：未检测到连接的设备
    echo    请手动安装APK：%CD%\%APK_PATH%
    del devices.txt
    goto :manual_install
)

echo ✅ 检测到连接的设备
adb install -r "%APK_PATH%"
if %ERRORLEVEL% EQU 0 (
    echo ✅ APK安装成功
) else (
    echo ❌ 自动安装失败，请手动安装
)
del devices.txt

:manual_install
echo.
echo ========================================
echo 📱 测试步骤：
echo ========================================
echo 1. 如果自动安装失败，请手动安装APK：
echo    %CD%\%APK_PATH%
echo.
echo 2. 打开APP后，在地址栏输入：
echo    file:///android_asset/debug_test.html
echo.
echo 3. 按顺序测试以下功能：
echo    ✓ 检查权限状态
echo    ✓ 直接权限申请 (重点测试)
echo    ✓ 强制权限弹窗测试
echo    ✓ 直接读取通讯录
echo.
echo 4. 同时在电脑上运行ADB日志：
echo    adb logcat -s SuiYiHua
echo.
echo 5. 如果权限弹窗仍无响应，请反馈：
echo    - 手机型号和Android版本
echo    - 调试页面显示的状态信息
echo    - ADB日志输出
echo ========================================
echo.
pause
