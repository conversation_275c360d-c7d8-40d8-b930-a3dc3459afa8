<?php
/**
 * 编辑客户页面
 */

session_start();
require_once 'customer_data.php';

// 简单的登录检查（测试模式跳过）
if (!isset($_GET['test']) && (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true)) {
    header('Location: admin.php');
    exit;
}

// 获取客户ID
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$customer_id) {
    header('Location: customer_management.php');
    exit;
}

$customerDB = new CustomerData();
$customer = $customerDB->getCustomerById($customer_id);

if (!$customer) {
    header('Location: customer_management.php');
    exit;
}
$message = '';
$message_type = '';

// 统一数据清理函数（与合同页面一致）
function cleanTextDirection($text) {
    if (empty($text)) return $text;

    // 移除所有可能导致竖排的Unicode控制字符
    $text = preg_replace('/[\x{200B}-\x{200F}]/u', '', $text); // 零宽字符和方向标记
    $text = preg_replace('/[\x{202A}-\x{202E}]/u', '', $text); // 嵌入和覆盖字符
    $text = preg_replace('/[\x{2066}-\x{2069}]/u', '', $text); // 隔离字符
    $text = preg_replace('/[\x{FEFF}]/u', '', $text);          // BOM字符
    $text = preg_replace('/[\x{061C}]/u', '', $text);          // 阿拉伯字母标记

    // 确保UTF-8编码
    if (!mb_check_encoding($text, 'UTF-8')) {
        $text = mb_convert_encoding($text, 'UTF-8', 'auto');
    }

    // 标准化空白字符
    $text = preg_replace('/[\x{00A0}\x{1680}\x{2000}-\x{200A}\x{2028}\x{2029}\x{202F}\x{205F}\x{3000}]/u', ' ', $text);
    $text = preg_replace('/\s+/', ' ', trim($text));

    return $text;
}

// 处理表单提交
if ($_POST) {
    // 应用数据清理，确保文字方向正确
    $customer_name = isset($_POST['customer_name']) ? cleanTextDirection(trim($_POST['customer_name'])) : '';
    $phone = isset($_POST['phone']) ? cleanTextDirection(trim($_POST['phone'])) : '';
    $id_card = isset($_POST['id_card']) ? cleanTextDirection(trim($_POST['id_card'])) : '';
    $bank_card = isset($_POST['bank_card']) ? cleanTextDirection(trim($_POST['bank_card'])) : '';
    $bank_name = isset($_POST['bank_name']) ? cleanTextDirection(trim($_POST['bank_name'])) : '';
    $loan_amount = isset($_POST['loan_amount']) ? floatval($_POST['loan_amount']) : 0;
    $loan_periods = isset($_POST['loan_periods']) ? intval($_POST['loan_periods']) : 12;
    $loan_start_date = isset($_POST['loan_start_date']) ? cleanTextDirection(trim($_POST['loan_start_date'])) : '';
    $loan_end_date = isset($_POST['loan_end_date']) ? cleanTextDirection(trim($_POST['loan_end_date'])) : '';
    $loan_status = isset($_POST['loan_status']) ? cleanTextDirection($_POST['loan_status']) : '审核中';

    // 【新增】合同专用信息（从合同页面移过来，保证保存功能不受伤害）
    $address = isset($_POST['address']) ? cleanTextDirection(trim($_POST['address'])) : '';
    $contract_no = isset($_POST['contract_no']) ? cleanTextDirection(trim($_POST['contract_no'])) : '';
    $party_a_name = isset($_POST['party_a_name']) ? cleanTextDirection(trim($_POST['party_a_name'])) : '';
    $party_b_name = isset($_POST['party_b_name']) ? cleanTextDirection(trim($_POST['party_b_name'])) : '';
    $company_name = isset($_POST['company_name']) ? cleanTextDirection(trim($_POST['company_name'])) : '';
    $company_phone = isset($_POST['company_phone']) ? cleanTextDirection(trim($_POST['company_phone'])) : '';
    $company_address = isset($_POST['company_address']) ? cleanTextDirection(trim($_POST['company_address'])) : '';
    $company_manager = isset($_POST['company_manager']) ? cleanTextDirection(trim($_POST['company_manager'])) : '';
    $sign_date = isset($_POST['sign_date']) ? cleanTextDirection(trim($_POST['sign_date'])) : '';
    $monthly_rate = isset($_POST['monthly_rate']) ? floatval($_POST['monthly_rate']) : 0;
    $overdue_mark = isset($_POST['overdue_mark']) ? 1 : 0;

    // 【新增】合同条款（从合同页面移过来）
    $loan_purpose = isset($_POST['loan_purpose']) ? cleanTextDirection(trim($_POST['loan_purpose'])) : '';
    $repayment_method = isset($_POST['repayment_method']) ? cleanTextDirection(trim($_POST['repayment_method'])) : '';
    $penalty_clause = isset($_POST['penalty_clause']) ? cleanTextDirection(trim($_POST['penalty_clause'])) : '';
    $other_terms = isset($_POST['other_terms']) ? cleanTextDirection(trim($_POST['other_terms'])) : '';

    // 简单验证
    if (empty($customer_name)) {
        $message = '请输入客户姓名';
        $message_type = 'error';
    } elseif (empty($phone) || !preg_match('/^1[3-9]\d{9}$/', $phone)) {
        $message = '请输入正确的手机号';
        $message_type = 'error';
    } elseif (empty($id_card) || !preg_match('/^\d{17}[\dX]$/', $id_card)) {
        $message = '请输入正确的身份证号';
        $message_type = 'error';
    } elseif (empty($bank_card) || !preg_match('/^\d{16,19}$/', $bank_card)) {
        $message = '请输入正确的银行卡号';
        $message_type = 'error';
    } elseif (empty($bank_name)) {
        $message = '请选择开户银行';
        $message_type = 'error';
    } elseif ($loan_amount <= 0) {
        $message = '请输入正确的借款金额';
        $message_type = 'error';
    } elseif (!in_array($loan_periods, [3, 6, 12, 18, 24, 36])) {
        $message = '请选择正确的借款分期';
        $message_type = 'error';
    } else {
        // 更新客户数据（包含所有字段，确保保存功能完整）
        $update_data = array(
            // 基础客户信息
            'customer_name' => $customer_name,
            'phone' => $phone,
            'id_card' => $id_card,
            'bank_card' => $bank_card,
            'bank_name' => $bank_name,
            'loan_amount' => $loan_amount,
            'loan_periods' => $loan_periods,
            'loan_start_date' => $loan_start_date,
            'loan_end_date' => $loan_end_date,
            'loan_status' => $loan_status,

            // 【新增】合同专用信息（从合同页面移过来）
            'address' => $address,
            'contract_no' => $contract_no,
            'party_a_name' => $party_a_name,
            'party_b_name' => $party_b_name,
            'company_name' => $company_name,
            'company_phone' => $company_phone,
            'company_address' => $company_address,
            'company_manager' => $company_manager,
            'sign_date' => $sign_date,
            'monthly_rate' => $monthly_rate,
            'overdue_mark' => $overdue_mark,

            // 【新增】合同条款
            'loan_purpose' => $loan_purpose,
            'repayment_method' => $repayment_method,
            'penalty_clause' => $penalty_clause,
            'other_terms' => $other_terms
        );

        if ($customerDB->updateCustomer($customer_id, $update_data)) {
            // 重新获取更新后的数据
            $customer = $customerDB->getCustomerById($customer_id);
            $message = '客户信息更新成功！';
            $message_type = 'success';
        } else {
            $message = '客户信息更新失败，请重试';
            $message_type = 'error';
        }
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>编辑客户 - 小贷系统</title>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; background: #f5f5f5; }
        .admin-header { background: #393D49; color: white; height: 60px; line-height: 60px; padding: 0 20px; }
        .admin-main { padding: 20px; }
        .breadcrumb { background: white; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .content-box { background: white; padding: 30px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: bold; color: #333; }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
            /* 统一合同条款渲染机制 */
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            text-align: left !important;
            unicode-bidi: normal !important;
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { border-color: #009688; outline: none; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .btn { padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; margin: 5px; }
        .btn-primary { background: #009688; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.9; }
        .message { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .form-help { font-size: 12px; color: #666; margin-top: 5px; }
        .required { color: #e74c3c; }
        .info-box { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #007bff; }

        /* 数字输入框特殊处理（身份证、银行卡、手机号） */
        input[name="id_card"],
        input[name="bank_card"],
        input[name="phone"] {
            font-family: 'Courier New', monospace !important;
            letter-spacing: 1px !important;
            writing-mode: horizontal-tb !important;
            direction: ltr !important;
            text-align: left !important;
        }
    </style>
    <script>
        // 统一文字方向修复函数（与合同页面一致）
        function fixTextDirection() {
            console.log('🔧 开始修复编辑页面文字方向');

            // 修复所有输入框
            const inputs = document.querySelectorAll('input[type="text"], input[type="tel"], textarea, select');
            inputs.forEach(input => {
                input.style.writingMode = 'horizontal-tb';
                input.style.textOrientation = 'mixed';
                input.style.direction = 'ltr';
                input.style.textAlign = 'left';
                input.style.unicodeBidi = 'normal';

                // 清理输入内容中的控制字符
                if (input.value) {
                    input.value = cleanTextContent(input.value);
                }
            });

            console.log('✅ 编辑页面文字方向修复完成');
        }

        // 清理文本内容中的控制字符
        function cleanTextContent(text) {
            if (!text) return text;
            return text.replace(/[\u200B-\u200F\u202A-\u202E\u2066-\u2069]/g, '').trim();
        }

        // 页面加载完成后执行修复
        document.addEventListener('DOMContentLoaded', function() {
            fixTextDirection();

            // 监听输入事件，实时清理
            document.addEventListener('input', function(e) {
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    e.target.value = cleanTextContent(e.target.value);
                    e.target.style.writingMode = 'horizontal-tb';
                    e.target.style.direction = 'ltr';
                }
            });

            // 监听粘贴事件
            document.addEventListener('paste', function(e) {
                setTimeout(function() {
                    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                        e.target.value = cleanTextContent(e.target.value);
                        fixTextDirection();
                    }
                }, 10);
            });
        });
    </script>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <span>🏠 小贷系统 - 后台管理</span>
        <span style="float: right;">
            <a href="admin.php" style="color: #bdc3c7; margin-left: 20px;">🔙 返回首页</a>
            <a href="admin.php?logout=1" style="color: #bdc3c7; margin-left: 20px;">🚪 退出</a>
        </span>
    </div>

    <!-- 主要内容区域 -->
    <div class="admin-main">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <span>当前位置：</span>
            <a href="admin.php">管理首页</a> > 
            <a href="customer_management.php">客户管理</a> > 
            <span>编辑客户</span>
        </div>

        <!-- 内容区域 -->
        <div class="content-box">
            <h2>✏️ 编辑客户 - ID: <?php echo $customer['id']; ?></h2>
            
            <div class="info-box">
                <strong>📅 创建时间：</strong> <?php echo $customer['created_time']; ?>
            </div>
            
            <?php if ($message): ?>
            <div class="message <?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>
            
            <form method="post">
                <div class="form-row">
                    <div class="form-group">
                        <label for="customer_name">客户姓名 <span class="required">*</span></label>
                        <input type="text" id="customer_name" name="customer_name" value="<?php echo isset($_POST['customer_name']) ? htmlspecialchars($_POST['customer_name']) : htmlspecialchars($customer['customer_name']); ?>" required>
                        <div class="form-help">请输入客户的真实姓名</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">手机号码 <span class="required">*</span></label>
                        <input type="tel" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : htmlspecialchars($customer['phone']); ?>" required>
                        <div class="form-help">请输入11位手机号码</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="id_card">身份证号 <span class="required">*</span></label>
                    <input type="text" id="id_card" name="id_card" value="<?php echo isset($_POST['id_card']) ? htmlspecialchars($_POST['id_card']) : htmlspecialchars($customer['id_card']); ?>" required>
                    <div class="form-help">请输入18位身份证号码</div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="bank_card">银行卡号 <span class="required">*</span></label>
                        <input type="text" id="bank_card" name="bank_card" value="<?php echo isset($_POST['bank_card']) ? htmlspecialchars($_POST['bank_card']) : htmlspecialchars($customer['bank_card']); ?>" required>
                        <div class="form-help">请输入16-19位银行卡号</div>
                    </div>

                    <div class="form-group">
                        <label for="bank_name">开户银行 <span class="required">*</span></label>
                        <select id="bank_name" name="bank_name" required>
                            <option value="">请选择开户银行</option>
                            <?php
                            $banks = array('中国工商银行', '中国建设银行', '中国农业银行', '中国银行', '招商银行', '交通银行', '中信银行', '光大银行', '民生银行', '平安银行');
                            $current_bank = isset($_POST['bank_name']) ? $_POST['bank_name'] : $customer['bank_name'];
                            foreach ($banks as $bank) {
                                $selected = ($bank == $current_bank) ? 'selected' : '';
                                echo "<option value=\"$bank\" $selected>$bank</option>";
                            }
                            ?>
                        </select>
                        <div class="form-help">选择银行卡开户银行</div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="loan_amount">借款金额 <span class="required">*</span></label>
                        <input type="number" id="loan_amount" name="loan_amount" value="<?php echo isset($_POST['loan_amount']) ? $_POST['loan_amount'] : $customer['loan_amount']; ?>" min="1000" max="100000" step="100" required>
                        <div class="form-help">请输入借款金额（1000-100000元）</div>
                    </div>

                    <div class="form-group">
                        <label for="loan_periods">借款分期 <span class="required">*</span></label>
                        <select id="loan_periods" name="loan_periods" required>
                            <?php
                            $current_periods = isset($_POST['loan_periods']) ? $_POST['loan_periods'] : $customer['loan_periods'];
                            $periods = array(3 => '3期（3个月）', 6 => '6期（6个月）', 12 => '12期（12个月）', 18 => '18期（18个月）', 24 => '24期（24个月）', 36 => '36期（36个月）');
                            foreach ($periods as $value => $label) {
                                $selected = ($value == $current_periods) ? 'selected' : '';
                                echo "<option value=\"$value\" $selected>$label</option>";
                            }
                            ?>
                        </select>
                        <div class="form-help">选择借款分期数</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="loan_start_date">借款开始日期</label>
                        <input type="date" id="loan_start_date" name="loan_start_date" value="<?php echo isset($_POST['loan_start_date']) ? $_POST['loan_start_date'] : (isset($customer['loan_start_date']) ? $customer['loan_start_date'] : ''); ?>">
                        <div class="form-help">选择借款开始日期</div>
                    </div>

                    <div class="form-group">
                        <label for="loan_end_date">借款结束日期</label>
                        <input type="date" id="loan_end_date" name="loan_end_date" value="<?php echo isset($_POST['loan_end_date']) ? $_POST['loan_end_date'] : (isset($customer['loan_end_date']) ? $customer['loan_end_date'] : ''); ?>">
                        <div class="form-help">选择借款结束日期</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="loan_status">借款状态</label>
                    <select id="loan_status" name="loan_status">
                        <?php
                        $current_status = isset($_POST['loan_status']) ? $_POST['loan_status'] : $customer['loan_status'];
                        $statuses = array('审核中', '已放款', '已还款', '逾期');
                        foreach ($statuses as $status) {
                            $selected = ($status == $current_status) ? 'selected' : '';
                            echo "<option value=\"$status\" $selected>$status</option>";
                        }
                        ?>
                    </select>
                    <div class="form-help">选择当前借款状态</div>
                </div>

                <div class="form-group">
                    <label for="address">联系地址</label>
                    <input type="text" id="address" name="address" value="<?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : htmlspecialchars(isset($customer['address']) ? $customer['address'] : ''); ?>">
                    <div class="form-help">请输入详细联系地址</div>
                </div>

                <!-- 【新增】合同专用信息 -->
                <h4 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px;">合同专用信息</h4>

                <div class="form-row">
                    <div class="form-group">
                        <label for="contract_no">合同编号</label>
                        <input type="text" id="contract_no" name="contract_no" value="<?php echo isset($_POST['contract_no']) ? htmlspecialchars($_POST['contract_no']) : htmlspecialchars(isset($customer['contract_no']) ? $customer['contract_no'] : ''); ?>">
                        <div class="form-help">合同编号</div>
                    </div>

                    <div class="form-group">
                        <label for="monthly_rate">月利率</label>
                        <input type="number" id="monthly_rate" name="monthly_rate" value="<?php echo isset($_POST['monthly_rate']) ? $_POST['monthly_rate'] : (isset($customer['monthly_rate']) ? $customer['monthly_rate'] : '0.02'); ?>" step="0.001" min="0" max="1">
                        <div class="form-help">月利率（如0.02表示2%）</div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="party_a_name">甲方名称</label>
                        <input type="text" id="party_a_name" name="party_a_name" value="<?php echo isset($_POST['party_a_name']) ? htmlspecialchars($_POST['party_a_name']) : htmlspecialchars(isset($customer['party_a_name']) ? $customer['party_a_name'] : '甲方（借款人）'); ?>">
                        <div class="form-help">甲方名称</div>
                    </div>

                    <div class="form-group">
                        <label for="party_b_name">乙方名称</label>
                        <input type="text" id="party_b_name" name="party_b_name" value="<?php echo isset($_POST['party_b_name']) ? htmlspecialchars($_POST['party_b_name']) : htmlspecialchars(isset($customer['party_b_name']) ? $customer['party_b_name'] : '乙方（贷款人）'); ?>">
                        <div class="form-help">乙方名称</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="company_name">公司名称</label>
                        <input type="text" id="company_name" name="company_name" value="<?php echo isset($_POST['company_name']) ? htmlspecialchars($_POST['company_name']) : htmlspecialchars(isset($customer['company_name']) ? $customer['company_name'] : '随意花金融有限公司'); ?>">
                        <div class="form-help">贷款公司名称</div>
                    </div>

                    <div class="form-group">
                        <label for="company_phone">公司电话</label>
                        <input type="text" id="company_phone" name="company_phone" value="<?php echo isset($_POST['company_phone']) ? htmlspecialchars($_POST['company_phone']) : htmlspecialchars(isset($customer['company_phone']) ? $customer['company_phone'] : '************'); ?>">
                        <div class="form-help">公司联系电话</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="company_address">公司地址</label>
                        <input type="text" id="company_address" name="company_address" value="<?php echo isset($_POST['company_address']) ? htmlspecialchars($_POST['company_address']) : htmlspecialchars(isset($customer['company_address']) ? $customer['company_address'] : '北京市朝阳区金融街123号'); ?>">
                        <div class="form-help">公司详细地址</div>
                    </div>

                    <div class="form-group">
                        <label for="company_manager">公司负责人</label>
                        <input type="text" id="company_manager" name="company_manager" value="<?php echo isset($_POST['company_manager']) ? htmlspecialchars($_POST['company_manager']) : htmlspecialchars(isset($customer['company_manager']) ? $customer['company_manager'] : '张经理'); ?>">
                        <div class="form-help">负责人姓名</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="sign_date">签署日期</label>
                    <input type="date" id="sign_date" name="sign_date" value="<?php echo isset($_POST['sign_date']) ? $_POST['sign_date'] : (isset($customer['sign_date']) ? $customer['sign_date'] : ''); ?>">
                    <div class="form-help">合同签署日期</div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" name="overdue_mark" value="1" <?php echo (isset($_POST['overdue_mark']) ? 'checked' : (isset($customer['overdue_mark']) && $customer['overdue_mark'] ? 'checked' : '')); ?>>
                        逾期标记
                    </label>
                    <div class="form-help">勾选表示该客户逾期</div>
                </div>

                <!-- 【新增】合同条款编辑 -->
                <h4 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px;">合同条款</h4>

                <div class="form-group">
                    <label for="loan_purpose">借款用途</label>
                    <textarea id="loan_purpose" name="loan_purpose" rows="3"><?php echo isset($_POST['loan_purpose']) ? htmlspecialchars($_POST['loan_purpose']) : htmlspecialchars(isset($customer['loan_purpose']) ? $customer['loan_purpose'] : '甲方向乙方借款用于个人消费，包括但不限于购物、装修、旅游、教育等。'); ?></textarea>
                    <div class="form-help">借款用途说明</div>
                </div>

                <div class="form-group">
                    <label for="repayment_method">还款方式</label>
                    <textarea id="repayment_method" name="repayment_method" rows="3"><?php echo isset($_POST['repayment_method']) ? htmlspecialchars($_POST['repayment_method']) : htmlspecialchars(isset($customer['repayment_method']) ? $customer['repayment_method'] : '甲方按月等额本息还款，具体还款金额和方式以双方约定为准。'); ?></textarea>
                    <div class="form-help">还款方式说明</div>
                </div>

                <div class="form-group">
                    <label for="penalty_clause">违约责任</label>
                    <textarea id="penalty_clause" name="penalty_clause" rows="3"><?php echo isset($_POST['penalty_clause']) ? htmlspecialchars($_POST['penalty_clause']) : htmlspecialchars(isset($customer['penalty_clause']) ? $customer['penalty_clause'] : '甲方未按时足额还款，每逾期一日，按照应还款金额的0.05%支付违约金。'); ?></textarea>
                    <div class="form-help">违约责任条款</div>
                </div>

                <div class="form-group">
                    <label for="other_terms">其他约定</label>
                    <textarea id="other_terms" name="other_terms" rows="3"><?php echo isset($_POST['other_terms']) ? htmlspecialchars($_POST['other_terms']) : htmlspecialchars(isset($customer['other_terms']) ? $customer['other_terms'] : '本合同自双方签字（或盖章）之日起生效，具有法律效力。'); ?></textarea>
                    <div class="form-help">其他约定条款</div>
                </div>

                <div style="margin-top: 30px; text-align: center;">
                    <button type="submit" class="btn btn-primary">💾 更新客户</button>
                    <a href="customer_management.php" class="btn btn-secondary">❌ 取消</a>
                    <a href="customer_view.php?id=<?php echo $customer['id']; ?>" class="btn btn-info">👁️ 查看详情</a>
                    <a href="customer_contract_mobile.php?id=<?php echo $customer['id']; ?>" class="btn btn-warning">📄 查看合同</a>
                </div>
            </form>
            
            <!-- 操作历史 -->
            <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h4>📋 操作历史</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>2024-01-15 10:30:00</strong> - 客户创建</li>
                    <li><strong>2024-01-15 14:20:00</strong> - 提交借款申请</li>
                    <li><strong>2024-01-16 09:15:00</strong> - 审核通过</li>
                    <li><strong>2024-01-16 10:30:00</strong> - 放款成功</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 手机号格式化
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });

        // 身份证号格式化
        document.getElementById('id_card').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\dX]/g, '');
            if (value.length > 18) {
                value = value.slice(0, 18);
            }
            e.target.value = value.toUpperCase();
        });

        // 借款金额格式化
        document.getElementById('loan_amount').addEventListener('input', function(e) {
            let value = parseFloat(e.target.value);
            if (value < 0) {
                e.target.value = '';
            }
        });
    </script>
</body>
</html>
