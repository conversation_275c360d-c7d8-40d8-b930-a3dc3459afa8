<?php
/**
 * 自动生成合同页面 - 点击合同按钮自动生成
 * 简洁版本，无编辑功能，纯展示
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'customer_data.php';

// 获取客户ID
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$is_user_mode = isset($_GET['user']) && $_GET['user'] == '1';

// 严格的权限检查
if (!$is_user_mode) {
    // 管理员模式需要严格验证
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        // 重定向到登录页面
        header('Location: admin_login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        exit('需要管理员权限访问');
    }
} else {
    // 用户模式需要验证用户登录
    if (!isset($_SESSION['user_phone']) || empty($_SESSION['user_phone'])) {
        // 重定向到用户登录页面
        header('Location: user_login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        exit('需要用户登录');
    }

    // 用户模式下，根据登录的手机号获取客户数据
    $customer = $customerDB->getCustomerByPhone($_SESSION['user_phone']);
    if (!$customer) {
        exit('未找到您的客户信息');
    }
    $customer_id = $customer['id'];
}

// 获取客户数据
$customerDB = new CustomerData();

// 如果不是用户模式，需要通过ID获取客户数据
if (!$is_user_mode) {
    $customer = $customerDB->getCustomerById($customer_id);
    if (!$customer) {
        echo "客户不存在";
        exit;
    }
}
// 用户模式下，$customer 已在权限检查中获取

// 计算借款信息
$loan_amount = floatval($customer['loan_amount']);
$loan_periods = intval($customer['loan_periods']);
$monthly_rate = isset($customer['monthly_rate']) ? floatval($customer['monthly_rate']) : 0.02;
$monthly_payment = $loan_amount * ($monthly_rate * pow(1 + $monthly_rate, $loan_periods)) / (pow(1 + $monthly_rate, $loan_periods) - 1);
$total_amount = $monthly_payment * $loan_periods;
$total_interest = $total_amount - $loan_amount;

// 生成合同编号
$contract_no = isset($customer['contract_no']) && !empty($customer['contract_no']) 
    ? $customer['contract_no'] 
    : 'HT' . date('Ymd') . str_pad($customer_id, 4, '0', STR_PAD_LEFT);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="mobile-web-app-capable" content="yes">
    <!-- 强制横排显示 -->
    <meta name="screen-orientation" content="landscape">
    <meta name="x5-orientation" content="landscape">
    <meta name="x5-fullscreen" content="true">
    <title>小额贷款借款合同</title>
    <style>
        /* 强制横排样式 - 防止APK竖排 + 统一字体 */
        * {
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            unicode-bidi: normal !important;
            /* 全局统一字体 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .contract-title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            /* 统一字体样式 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
            line-height: 1.8 !important;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }

        .contract-no {
            text-align: center;
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            /* 统一字体样式 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
            line-height: 1.8 !important;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
            /* 统一字体样式 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
            line-height: 1.8 !important;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .info-table td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
            writing-mode: horizontal-tb !important;
            direction: ltr !important;
            /* 统一字体样式 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
            line-height: 1.8 !important;
            font-size: 14px !important;
        }
        
        .info-table .label {
            background: #f8f9fa;
            font-weight: bold;
            width: 30%;
            min-width: 100px;
            white-space: nowrap;
        }
        
        .contract-clause {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            /* 统一字体样式 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }

        .contract-clause p {
            margin-bottom: 15px;
            line-height: 1.8;
            /* 统一字体样式 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
            font-size: 14px !important;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
            text-align: left !important;
        }

        .signature-area {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
            /* 统一字体样式 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }

        .signature-box {
            width: 45%;
            /* 统一字体样式 - 与合同条款一致 */
            font-family: 'Microsoft YaHei', Arial, sans-serif !important;
            line-height: 1.8 !important;
            writing-mode: horizontal-tb !important;
            text-orientation: mixed !important;
            direction: ltr !important;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        
        /* 数字内容特殊样式 */
        .number {
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
        }
        
        /* 逾期标记 */
        .overdue {
            color: #dc3545;
            font-weight: bold;
        }
        
        /* 移动端适配 + 强化横排修复 */
        @media (max-width: 768px) {
            .container { padding: 15px; }
            .contract-title { font-size: 24px; }
            .signature-area { flex-direction: column; }
            .signature-box { width: 100%; margin-bottom: 20px; }

            /* 移动端强制横排 - 针对手机浏览器 */
            * {
                writing-mode: horizontal-tb !important;
                text-orientation: mixed !important;
                direction: ltr !important;
                unicode-bidi: normal !important;
                text-align: left !important;
                font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif !important;
                -webkit-writing-mode: horizontal-tb !important;
                -moz-writing-mode: horizontal-tb !important;
                -ms-writing-mode: horizontal-tb !important;
            }

            /* 移动端表格强制横排 + 优化布局 */
            .info-table, .info-table td, .info-table th {
                writing-mode: horizontal-tb !important;
                text-orientation: mixed !important;
                direction: ltr !important;
                text-align: left !important;
                display: table-cell !important;
                vertical-align: middle !important;
                -webkit-writing-mode: horizontal-tb !important;
                -moz-writing-mode: horizontal-tb !important;
                -ms-writing-mode: horizontal-tb !important;
                word-wrap: break-word !important;
                word-break: normal !important;
                white-space: normal !important;
            }

            /* 移动端标签列宽度优化 */
            .info-table .label {
                width: 35% !important;
                min-width: 80px !important;
                max-width: 120px !important;
                padding: 8px !important;
                font-size: 13px !important;
            }

            /* 移动端内容列优化 */
            .info-table td:not(.label) {
                width: 65% !important;
                padding: 8px !important;
                font-size: 14px !important;
                line-height: 1.4 !important;
            }

            /* 移动端文字内容强制横排 */
            p, span, div, h1, h2, h3, h4, h5, h6, strong, b {
                writing-mode: horizontal-tb !important;
                text-orientation: mixed !important;
                direction: ltr !important;
                text-align: left !important;
                display: inline-block !important;
                width: auto !important;
                -webkit-writing-mode: horizontal-tb !important;
                -moz-writing-mode: horizontal-tb !important;
                -ms-writing-mode: horizontal-tb !important;
            }
        }
        
        /* 打印样式 */
        @media print {
            .actions { display: none; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 合同标题 -->
        <div class="contract-title">
            小额贷款借款合同
            <?php if (isset($customer['overdue_mark']) && $customer['overdue_mark'] == 1): ?>
                <span class="overdue">【逾期】</span>
            <?php endif; ?>
        </div>
        <div class="contract-no">合同编号：<?php echo htmlspecialchars($contract_no); ?></div>

        <!-- 甲方信息 -->
        <div class="section">
            <div class="section-title">甲方（借款人）信息</div>
            <table class="info-table">
                <tr>
                    <td class="label">姓名</td>
                    <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                </tr>
                <tr>
                    <td class="label">身份证号</td>
                    <td class="number"><?php echo htmlspecialchars($customer['id_card']); ?></td>
                </tr>
                <tr>
                    <td class="label">联系电话</td>
                    <td class="number"><?php echo htmlspecialchars($customer['phone']); ?></td>
                </tr>
                <tr>
                    <td class="label">联系地址</td>
                    <td><?php echo htmlspecialchars(isset($customer['address']) ? $customer['address'] : ''); ?></td>
                </tr>
                <tr>
                    <td class="label">银行卡号</td>
                    <td class="number"><?php echo htmlspecialchars(isset($customer['bank_card']) ? $customer['bank_card'] : ''); ?></td>
                </tr>
                <tr>
                    <td class="label">开户银行</td>
                    <td><?php echo htmlspecialchars(isset($customer['bank_name']) ? $customer['bank_name'] : ''); ?></td>
                </tr>
            </table>
        </div>

        <!-- 乙方信息 -->
        <div class="section">
            <div class="section-title">乙方（贷款人）信息</div>
            <table class="info-table">
                <tr>
                    <td class="label">公司名称</td>
                    <td><?php echo htmlspecialchars(isset($customer['company_name']) ? $customer['company_name'] : '随意花金融有限公司'); ?></td>
                </tr>
                <tr>
                    <td class="label">联系电话</td>
                    <td class="number"><?php echo htmlspecialchars(isset($customer['company_phone']) ? $customer['company_phone'] : '************'); ?></td>
                </tr>
                <tr>
                    <td class="label">公司地址</td>
                    <td><?php echo htmlspecialchars(isset($customer['company_address']) ? $customer['company_address'] : '北京市朝阳区金融街123号'); ?></td>
                </tr>
                <tr>
                    <td class="label">负责人</td>
                    <td><?php echo htmlspecialchars(isset($customer['company_manager']) ? $customer['company_manager'] : '张经理'); ?></td>
                </tr>
            </table>
        </div>

        <!-- 借款详情 -->
        <div class="section">
            <div class="section-title">借款详情</div>
            <table class="info-table">
                <tr>
                    <td class="label">借款金额</td>
                    <td>人民币 <strong class="number"><?php echo number_format($loan_amount, 2); ?></strong> 元</td>
                </tr>
                <tr>
                    <td class="label">借款期限</td>
                    <td class="number"><?php echo $loan_periods; ?> 个月</td>
                </tr>
                <tr>
                    <td class="label">月利率</td>
                    <td class="number"><?php echo ($monthly_rate * 100); ?>%</td>
                </tr>
                <tr>
                    <td class="label">总利息</td>
                    <td>人民币 <span class="number"><?php echo number_format($total_interest, 2); ?></span> 元</td>
                </tr>
                <tr>
                    <td class="label">月还款金额</td>
                    <td>人民币 <span class="number"><?php echo number_format($monthly_payment, 2); ?></span> 元</td>
                </tr>
                <tr>
                    <td class="label">总还款金额</td>
                    <td>人民币 <span class="number"><?php echo number_format($total_amount, 2); ?></span> 元</td>
                </tr>
                <tr>
                    <td class="label">借款状态</td>
                    <td>
                        <span class="<?php echo (isset($customer['loan_status']) && $customer['loan_status'] == '逾期') ? 'overdue' : ''; ?>">
                            <?php echo htmlspecialchars(isset($customer['loan_status']) ? $customer['loan_status'] : '已放款'); ?>
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        <!-- 合同条款 -->
        <div class="section">
            <div class="section-title">合同条款</div>
            <div class="contract-clause">
                <p><strong>第一条 借款用途：</strong><?php echo htmlspecialchars(isset($customer['loan_purpose']) && !empty($customer['loan_purpose']) ? $customer['loan_purpose'] : '甲方向乙方借款用于个人消费，包括但不限于购物、装修、旅游、教育等。'); ?></p>

                <p><strong>第二条 借款期限：</strong>自本合同生效之日起，至最后一期还款日止，共计 <?php echo $loan_periods; ?> 个月。</p>

                <p><strong>第三条 还款方式：</strong><?php echo htmlspecialchars(isset($customer['repayment_method']) && !empty($customer['repayment_method']) ? $customer['repayment_method'] : '甲方按月等额本息还款，每月还款金额为人民币 ' . number_format($monthly_payment, 2) . ' 元。'); ?></p>

                <p><strong>第四条 违约责任：</strong><?php echo htmlspecialchars(isset($customer['penalty_clause']) && !empty($customer['penalty_clause']) ? $customer['penalty_clause'] : '甲方未按时足额还款，每逾期一日，按照应还款金额的0.05%支付违约金。'); ?></p>

                <p><strong>第五条 其他约定：</strong><?php echo htmlspecialchars(isset($customer['other_terms']) && !empty($customer['other_terms']) ? $customer['other_terms'] : '本合同自双方签字（或盖章）之日起生效，具有法律效力。'); ?></p>
            </div>
        </div>

        <!-- 签署区域 -->
        <div class="signature-area">
            <div class="signature-box">
                <p>甲方（借款人）签字：</p>
                <p style="border-bottom: 1px solid #000; height: 50px; margin-bottom: 20px;"></p>
            </div>
            <div class="signature-box">
                <p>乙方（贷款人）：随意花金融有限公司</p>
                <p>签字：</p>
                <p style="border-bottom: 1px solid #000; height: 50px; margin-bottom: 20px;"></p>
            </div>
        </div>

        <p style="text-align: center; margin-top: 30px;">
            签署日期：<?php echo htmlspecialchars(isset($customer['sign_date']) && !empty($customer['sign_date']) ? $customer['sign_date'] : date('Y 年 m 月 d 日')); ?>
        </p>

        <!-- 操作按钮 -->
        <div class="actions">
            <button onclick="window.print()" class="btn btn-primary">🖨️ 打印合同</button>
            <?php if (!$is_user_mode): ?>
                <a href="customer_edit.php?id=<?php echo $customer_id; ?>" class="btn btn-warning">✏️ 编辑客户</a>
                <a href="customer_management.php" class="btn btn-secondary">📋 返回列表</a>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // 移动端强化横排修复
        function fixMobileTextDirection() {
            console.log('🔧 开始移动端文字方向修复');

            // 检测是否为移动设备
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;

            if (isMobile) {
                console.log('📱 检测到移动设备，应用强化修复');

                // 强制所有元素横排 - 移动端专用
                const allElements = document.querySelectorAll('*');
                allElements.forEach(element => {
                    element.style.writingMode = 'horizontal-tb';
                    element.style.textOrientation = 'mixed';
                    element.style.direction = 'ltr';
                    element.style.textAlign = 'left';
                    element.style.unicodeBidi = 'normal';

                    // 添加浏览器前缀支持
                    element.style.webkitWritingMode = 'horizontal-tb';
                    element.style.mozWritingMode = 'horizontal-tb';
                    element.style.msWritingMode = 'horizontal-tb';

                    // 移除可能的竖排类名
                    element.classList.remove('vertical', 'vertical-text', 'v-text');
                });

                // 特别处理表格
                const tables = document.querySelectorAll('table, td, th');
                tables.forEach(table => {
                    table.style.writingMode = 'horizontal-tb';
                    table.style.direction = 'ltr';
                    table.style.textAlign = 'left';
                    table.style.display = table.tagName === 'TABLE' ? 'table' : 'table-cell';
                });

                // 特别处理文字内容
                const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, strong, b, label');
                textElements.forEach(el => {
                    el.style.writingMode = 'horizontal-tb';
                    el.style.direction = 'ltr';
                    el.style.textAlign = 'left';
                    el.style.display = 'inline-block';
                    el.style.width = 'auto';
                });

                console.log('✅ 移动端文字方向修复完成');
            } else {
                console.log('🖥️ 桌面设备，应用标准修复');

                // 桌面端标准修复
                const allElements = document.querySelectorAll('*');
                allElements.forEach(element => {
                    element.style.writingMode = 'horizontal-tb';
                    element.style.textOrientation = 'mixed';
                    element.style.direction = 'ltr';
                });
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ 自动生成合同页面加载完成');
            fixMobileTextDirection();
        });

        // 窗口大小改变时重新修复
        window.addEventListener('resize', function() {
            setTimeout(fixMobileTextDirection, 100);
        });

        // 页面显示时重新修复（处理浏览器前进后退）
        window.addEventListener('pageshow', function() {
            setTimeout(fixMobileTextDirection, 100);
        });
    </script>
</body>
</html>
