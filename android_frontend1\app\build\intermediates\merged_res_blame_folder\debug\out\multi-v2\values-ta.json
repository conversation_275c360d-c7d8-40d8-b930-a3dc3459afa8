{"logs": [{"outputFile": "com.dailuanshej.loan.app-mergeDebugResources-27:/values-ta/values-ta.xml", "map": [{"source": "E:\\SDK\\caches\\transforms-3\\7cfde09bef56db81a739e41054cc6284\\transformed\\material-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,356,465,583,667,731,839,907,968,1076,1162,1220,1304,1371,1425,1548,1610,1673,1727,1815,1943,2029,2111,2243,2323,2404,2493,2550,2602,2668,2753,2841,2933,3002,3079,3159,3227,3326,3409,3501,3595,3669,3755,3849,3899,3965,4050,4137,4200,4265,4328,4436,4539,4637,4742,4803,4859", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,131,79,80,88,56,51,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,60,55,85", "endOffsets": "267,351,460,578,662,726,834,902,963,1071,1157,1215,1299,1366,1420,1543,1605,1668,1722,1810,1938,2024,2106,2238,2318,2399,2488,2545,2597,2663,2748,2836,2928,2997,3074,3154,3222,3321,3404,3496,3590,3664,3750,3844,3894,3960,4045,4132,4195,4260,4323,4431,4534,4632,4737,4798,4854,4940"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3088,3172,3281,3399,3483,3547,3655,3723,3784,3892,3978,4036,4120,4187,4241,4364,4426,4489,4543,4631,4759,4845,4927,5059,5139,5220,5309,5366,5418,5484,5569,5657,5749,5818,5895,5975,6043,6142,6225,6317,6411,6485,6571,6665,6715,6781,6866,6953,7016,7081,7144,7252,7355,7453,7558,7619,7675", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,131,79,80,88,56,51,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,60,55,85", "endOffsets": "317,3167,3276,3394,3478,3542,3650,3718,3779,3887,3973,4031,4115,4182,4236,4359,4421,4484,4538,4626,4754,4840,4922,5054,5134,5215,5304,5361,5413,5479,5564,5652,5744,5813,5890,5970,6038,6137,6220,6312,6406,6480,6566,6660,6710,6776,6861,6948,7011,7076,7139,7247,7350,7448,7553,7614,7670,7756"}}, {"source": "E:\\SDK\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "7843", "endColumns": "100", "endOffsets": "7939"}}, {"source": "E:\\SDK\\caches\\transforms-3\\ae146d59d58c77d62f24ab479ef1acb9\\transformed\\appcompat-1.5.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,7761", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,7838"}}]}]}