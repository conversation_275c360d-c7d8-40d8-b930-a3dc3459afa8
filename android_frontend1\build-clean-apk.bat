@echo off
echo ========================================
echo 🧹 构建清洁版APK - 无测试页面
echo ========================================

echo.
echo 📋 清洁版特性：
echo   ✅ 删除所有测试页面文件
echo   ✅ 启动直接加载正常首页
echo   ✅ 印章显示优化完整保留
echo   ✅ 合同功能完全正常
echo   ✅ 无任何测试页面干扰
echo.

echo 🔧 步骤1: 验证测试文件已删除...
if exist "..\源码\apk_test.php" (
    echo ❌ 测试文件仍存在
    pause
    exit /b 1
) else (
    echo ✅ 服务器测试文件已删除
)

if exist "..\源码\apk_simple_test.html" (
    echo ❌ 简单测试文件仍存在
    pause
    exit /b 1
) else (
    echo ✅ 简单测试文件已删除
)

echo.
echo 🔧 步骤2: 验证首页加载配置...
findstr /C:"https://dailuanshej.cn" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ 首页URL配置正确
) else (
    echo ❌ 首页URL配置错误
    pause
    exit /b 1
)

findstr /C:"不加载任何测试页面" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ 确认不加载测试页面
) else (
    echo ❌ 测试页面检查失败
    pause
    exit /b 1
)

echo.
echo 🔧 步骤3: 清理构建环境...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 已清理构建缓存
)

if exist "build" (
    rmdir /s /q "build"
    echo ✅ 已清理项目缓存
)

echo.
echo 🔧 步骤4: 构建清洁版APK...

call .\gradlew clean
if %errorlevel% neq 0 (
    echo ⚠️ Clean有警告，继续构建...
)

echo 正在构建清洁版APK（无测试页面）...
call .\gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo 📦 步骤5: 生成清洁版APK...
set APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk
set APK_DEST=youyihua_clean.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%"
    echo ✅ 清洁版APK已生成: %APK_DEST%
    
    REM 显示文件信息
    for %%A in ("%APK_DEST%") do (
        set APK_SIZE=%%~zA
        set APK_DATE=%%~tA
    )
    echo   文件大小: %APK_SIZE% 字节
    echo   创建时间: %APK_DATE%
    
) else (
    echo ❌ 找不到构建的APK文件
    pause
    exit /b 1
)

echo.
echo 📤 步骤6: 部署清洁版APK...
if exist "..\源码\apk\" (
    copy "%APK_DEST%" "..\源码\apk\%APK_DEST%"
    echo ✅ APK已部署到服务器目录
)

echo.
echo 🧹 步骤7: 清理旧版本APK...
if exist "youyihua_debug.apk" (
    del "youyihua_debug.apk"
    echo ✅ 已删除调试版APK
)

if exist "youyihua_fixed.apk" (
    del "youyihua_fixed.apk"
    echo ✅ 已删除修复版APK
)

if exist "youyihua_seal_optimized.apk" (
    del "youyihua_seal_optimized.apk"
    echo ✅ 已删除印章优化版APK
)

if exist "youyihua_web_test.apk" (
    del "youyihua_web_test.apk"
    echo ✅ 已删除Web测试版APK
)

echo.
echo ========================================
echo 🎉 清洁版APK构建完成！
echo ========================================
echo.
echo 📱 清洁版特性确认：
echo   🏠 启动页面: https://dailuanshej.cn (正常首页)
echo   🚫 无测试页面: 所有测试文件已删除
echo   📄 合同功能: 完全正常，支持Web端所有功能
echo   🖼️ 印章显示: 优化版本，不被压缩
echo   🖨️ 打印功能: 完全支持
echo   📱 移动优化: 完整的移动端适配
echo.
echo 🧪 安装测试：
echo   1. 完全卸载之前的所有版本
echo   2. 安装清洁版: %APK_DEST%
echo   3. 打开应用 → 直接显示正常首页
echo   4. 手机号登录 → 查看合同
echo   5. 验证印章显示正常
echo   6. 测试打印功能
echo.
echo ✅ 确认事项：
echo   • 启动不会显示任何测试页面
echo   • 直接进入正常的登录首页
echo   • 合同页面完全正常
echo   • 印章不被压缩，显示清晰
echo   • 所有功能与Web端一致
echo.
pause
