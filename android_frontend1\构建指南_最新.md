# Android APK 构建指南 - 最新版本

## 📋 概述
本指南将帮助您使用 E:\SDK 目录中的 Android SDK 和 JDK 来构建随意花小贷 APK。

## 🚀 快速开始

### 方法一：一键构建（推荐）
```bash
# 在 android_frontend 目录中运行
build_final_v2.bat
```

### 方法二：先设置JDK，再构建
```bash
# 步骤1：设置JDK
setup_jdk_sdk.bat

# 步骤2：构建APK
gradlew.bat clean
gradlew.bat assembleDebug
```

### 方法三：手动检测JDK
```bash
# 检测可用的JDK
detect_jdk.bat

# 然后运行构建
build_final_v2.bat
```

## 📁 脚本说明

### 🔧 主要构建脚本
- **`build_final_v2.bat`** - 最新的一键构建脚本，自动检测JDK
- **`build_final.bat`** - 原版构建脚本
- **`build_clean.bat`** - 清理构建脚本

### 🔍 JDK检测和设置
- **`detect_jdk.bat`** - 智能JDK检测工具
- **`setup_jdk_sdk.bat`** - 针对E:\SDK的JDK设置工具
- **`fix_java.bat`** - Java环境修复工具

### 🛠️ 其他工具
- **`build_apk_simple.bat`** - 简化版构建脚本
- **`build_with_sdk.bat`** - 使用SDK构建

## 🎯 推荐流程

### 首次使用
1. 运行 `setup_jdk_sdk.bat` 设置JDK环境
2. 运行 `build_final_v2.bat` 开始构建
3. 等待构建完成，APK将保存到根目录

### 后续使用
直接运行 `build_final_v2.bat` 即可

## 📱 输出文件
构建成功后，APK文件将位于：
- **调试版本**: `../随意花小贷-debug.apk`
- **源文件**: `app/build/outputs/apk/debug/app-debug.apk`

## 🔧 环境要求
- ✅ Android SDK: E:\SDK
- ✅ JDK: E:\SDK\jdks\* (由Android Studio自动安装)
- ✅ Gradle: 使用项目内置版本
- ✅ 目标Android版本: 7.0+ (API 24+)

## ❌ 常见问题解决

### 问题1: 找不到JDK
**现象**: "No JDK found" 或 "JAVA_HOME not set"
**解决方案**:
```bash
# 运行JDK设置工具
setup_jdk_sdk.bat
```

### 问题2: 构建失败
**现象**: Gradle构建报错
**解决方案**:
```bash
# 清理并重新构建
gradlew.bat clean
gradlew.bat assembleDebug --stacktrace
```

### 问题3: 权限问题
**现象**: "Access denied" 或权限错误
**解决方案**:
- 以管理员身份运行命令提示符
- 检查防病毒软件是否阻止了构建过程

### 问题4: SDK路径错误
**现象**: "SDK not found"
**解决方案**:
检查 `local.properties` 文件中的SDK路径：
```
sdk.dir=E:\\SDK
```

## 📊 构建日志分析
如果构建失败，检查以下关键信息：
1. **JDK版本**: 应该是11或17
2. **Gradle版本**: 8.5+
3. **Android Gradle Plugin**: 8.1.2+
4. **编译SDK版本**: 34

## 🎉 成功标志
看到以下信息表示构建成功：
```
✅ Build successful!
✅ APK copied to: ../随意花小贷-debug.apk
🎉 Build complete! You can now install the APK on your device.
```

## 📞 技术支持
如果遇到问题，请提供：
1. 完整的错误日志
2. JDK版本信息 (`java -version`)
3. Gradle版本信息 (`gradlew.bat --version`)
4. 使用的构建脚本名称

---
*最后更新: 2024年12月*
