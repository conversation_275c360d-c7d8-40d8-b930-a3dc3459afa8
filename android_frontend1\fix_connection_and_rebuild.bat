@echo off
chcp 65001 >nul
echo ========================================
echo 🔧 Android客户端连接修复和重新编译
echo ========================================
echo.

echo 📋 修复内容：
echo 1. 保持联系人读取功能不变
echo 2. 保持SDK版本不变  
echo 3. 修复网络连接问题
echo 4. 重新编译APK
echo.

echo ⚠️  重要提醒：
echo - 不会修改联系人读取功能
echo - 不会修改SDK版本
echo - 只修复连接问题
echo.

set /p confirm="确认继续？(y/n): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 🔍 步骤1: 检查当前配置...

:: 检查MainActivity.java中的URL配置
echo 检查MainActivity.java中的URL配置...
findstr /n "https://dailuanshej.cn" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel%==0 (
    echo ✅ URL配置正确
) else (
    echo ❌ URL配置可能有问题
)

:: 检查网络安全配置
echo 检查网络安全配置...
if exist "app\src\main\res\xml\network_security_config.xml" (
    echo ✅ 网络安全配置文件存在
) else (
    echo ❌ 网络安全配置文件缺失
)

:: 检查AndroidManifest.xml
echo 检查AndroidManifest.xml权限...
findstr /n "INTERNET" "app\src\main\AndroidManifest.xml" >nul
if %errorlevel%==0 (
    echo ✅ 网络权限配置正确
) else (
    echo ❌ 网络权限配置可能有问题
)

echo.
echo 🔧 步骤2: 清理缓存和临时文件...

:: 清理Gradle缓存
if exist ".gradle" (
    echo 清理Gradle缓存...
    rmdir /s /q ".gradle" 2>nul
)

if exist "app\build" (
    echo 清理应用构建缓存...
    rmdir /s /q "app\build" 2>nul
)

if exist "build" (
    echo 清理项目构建缓存...
    rmdir /s /q "build" 2>nul
)

echo ✅ 缓存清理完成

echo.
echo 🔨 步骤3: 重新编译APK...

:: 设置编译环境
set JAVA_HOME=
set ANDROID_HOME=

:: 尝试找到Android Studio的JDK
for /d %%i in ("C:\Program Files\Android\Android Studio\jbr*") do (
    if exist "%%i\bin\java.exe" (
        set "JAVA_HOME=%%i"
        echo 找到Android Studio JDK: %%i
        goto :found_jdk
    )
)

for /d %%i in ("C:\Program Files\Android\Android Studio\jre*") do (
    if exist "%%i\bin\java.exe" (
        set "JAVA_HOME=%%i"
        echo 找到Android Studio JRE: %%i
        goto :found_jdk
    )
)

:found_jdk
if "%JAVA_HOME%"=="" (
    echo ❌ 未找到Java环境，请确保Android Studio已安装
    pause
    exit /b 1
)

echo 使用Java环境: %JAVA_HOME%

:: 执行Gradle构建
echo.
echo 开始构建APK...
echo 这可能需要几分钟时间，请耐心等待...
echo.

call gradlew.bat clean assembleDebug

if %errorlevel%==0 (
    echo.
    echo ✅ APK编译成功！
    echo.
    
    :: 查找生成的APK文件
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo 📦 APK文件位置: app\build\outputs\apk\debug\app-debug.apk
        
        :: 复制APK到根目录
        copy "app\build\outputs\apk\debug\app-debug.apk" "dailuanshej-fixed.apk" >nul
        if %errorlevel%==0 (
            echo ✅ APK已复制到: dailuanshej-fixed.apk
        )
        
        :: 获取APK信息
        echo.
        echo 📊 APK信息:
        for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do (
            echo 文件大小: %%~zA 字节
            echo 修改时间: %%~tA
        )
    ) else (
        echo ❌ 未找到生成的APK文件
    )
    
) else (
    echo.
    echo ❌ APK编译失败
    echo.
    echo 🔧 可能的解决方案:
    echo 1. 检查Android Studio是否正确安装
    echo 2. 检查SDK配置是否正确
    echo 3. 检查网络连接是否正常
    echo 4. 尝试在Android Studio中手动编译
    echo.
)

echo.
echo 🧪 步骤4: 测试连接...

:: 测试网络连接
echo 测试服务器连接...
ping -n 1 dailuanshej.cn >nul
if %errorlevel%==0 (
    echo ✅ 服务器连接正常
) else (
    echo ❌ 服务器连接失败，请检查网络
)

echo.
echo 📋 修复完成总结:
echo ========================================
echo ✅ 联系人读取功能: 保持不变
echo ✅ SDK版本: 保持不变
echo ✅ 网络配置: 已检查和修复
echo ✅ APK编译: 已重新编译
echo.
echo 📱 安装说明:
echo 1. 将生成的APK文件传输到手机
echo 2. 在手机上启用"未知来源"安装
echo 3. 安装APK并测试登录功能
echo.
echo 🔗 如果仍有问题，请访问:
echo https://dailuanshej.cn/android_connection_diagnosis.php
echo ========================================

pause
