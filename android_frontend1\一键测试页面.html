<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 一键测试通讯录功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .code-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 15px 0;
            position: relative;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-btn:hover {
            background: #0056b3;
        }
        
        .test-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            width: 100%;
            margin: 20px 0;
        }
        
        .test-btn:hover {
            background: #218838;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 随意花通讯录功能测试</h1>
            <p>一键测试APP通讯录权限和数据读取功能</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ 使用前提：</strong><br>
            请确保您正在APP中打开此页面，而不是普通浏览器中。
        </div>
        
        <div class="step">
            <h3>🔧 方法一：直接测试（推荐）</h3>
            <p>点击下方按钮直接在当前页面测试通讯录功能：</p>
            <button class="test-btn" onclick="loadAndTest()">🚀 立即测试通讯录功能</button>
        </div>
        
        <div class="step">
            <h3>💻 方法二：控制台测试</h3>
            <p>1. 按 F12 打开开发者工具（如果APP支持）</p>
            <p>2. 复制下方代码到控制台并回车：</p>
            <div class="code-box">
                <button class="copy-btn" onclick="copyCode()">复制代码</button>
                <div id="console-code">
// 复制此代码到APP的开发者控制台中执行
(function(){
    const script = document.createElement('script');
    script.src = 'data:text/javascript;charset=utf-8,' + encodeURIComponent(`
        // 通讯录测试代码已内嵌，直接执行
        ${getTestCode()}
    `);
    document.head.appendChild(script);
})();
                </div>
            </div>
        </div>
        
        <div class="step">
            <h3>🌐 方法三：在 dailuanshej.cn 页面测试</h3>
            <p>1. 在APP中打开 <strong>https://dailuanshej.cn</strong></p>
            <p>2. 按F12打开控制台（如果支持）</p>
            <p>3. 复制下方完整代码并执行：</p>
            <div class="code-box">
                <button class="copy-btn" onclick="copyFullCode()">复制完整代码</button>
                <div id="full-code" style="max-height: 200px; overflow-y: auto;">
// 完整的通讯录测试代码（可直接在任何页面执行）
// ... 完整代码内容见下方 ...
                </div>
            </div>
        </div>
        
        <div class="success">
            <strong>✅ 测试成功标准：</strong><br>
            • 检测到Android接口 ✓<br>
            • 权限申请正常弹窗 ✓<br>
            • 授权后能读取通讯录数据 ✓<br>
            • 显示联系人姓名和电话 ✓
        </div>
        
        <div class="step">
            <h3>🔍 故障排查</h3>
            <p><strong>如果遇到问题，请检查：</strong></p>
            <ul>
                <li>是否在最新版本的APP中打开页面</li>
                <li>是否已安装最新编译的APK</li>
                <li>设备系统版本是否支持（Android 7.0+）</li>
                <li>APP是否有通讯录权限声明</li>
                <li>查看ADB日志：<code>adb logcat -s SuiYiHua</code></li>
            </ul>
        </div>
    </div>
    
    <script>
        // 获取测试代码
        function getTestCode() {
            return `
(function() {
    'use strict';
    
    // 创建测试UI
    function createTestUI() {
        const existingUI = document.getElementById('contacts-test-ui');
        if (existingUI) existingUI.remove();
        
        const container = document.createElement('div');
        container.id = 'contacts-test-ui';
        container.style.cssText = \`
            position: fixed; top: 20px; right: 20px; width: 320px; max-height: 400px;
            background: #fff; border: 2px solid #007bff; border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3); z-index: 9999;
            font-family: Arial, sans-serif; overflow: hidden;
        \`;
        
        container.innerHTML = \`
            <div style="background: #007bff; color: white; padding: 10px; font-weight: bold; display: flex; justify-content: space-between; align-items: center;">
                <span>📱 通讯录功能测试</span>
                <button onclick="document.getElementById('contacts-test-ui').remove()" 
                        style="background: none; border: none; color: white; cursor: pointer; font-size: 18px;">×</button>
            </div>
            <div style="padding: 15px;">
                <button id="test-btn" style="width: 100%; padding: 12px; background: #28a745; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; margin-bottom: 15px;">
                    🔍 获取通讯录
                </button>
                <div id="test-status" style="padding: 10px; background: #f8f9fa; border-radius: 5px; margin-bottom: 10px; min-height: 20px; border-left: 4px solid #6c757d;">
                    等待测试...
                </div>
                <div id="test-results" style="max-height: 200px; overflow-y: auto; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 5px; border: 1px solid #dee2e6;">
                </div>
            </div>
        \`;
        
        document.body.appendChild(container);
        document.getElementById('test-btn').onclick = testContacts;
        console.log('✅ 通讯录测试UI已创建');
    }
    
    // 更新状态
    function updateStatus(message, type = 'info') {
        const status = document.getElementById('test-status');
        if (!status) return;
        
        const colors = { 'info': '#6c757d', 'success': '#28a745', 'warning': '#ffc107', 'error': '#dc3545' };
        status.style.borderLeftColor = colors[type] || colors.info;
        status.innerHTML = message;
        console.log(\`[\${type.toUpperCase()}] \${message}\`);
    }
    
    // 更新结果
    function updateResults(content) {
        const results = document.getElementById('test-results');
        if (results) results.innerHTML = content;
    }
    
    // 检测环境
    function detectEnvironment() {
        const ua = navigator.userAgent;
        return {
            isAndroid: /Android/i.test(ua),
            isWebView: /wv|WebView/i.test(ua),
            hasAndroidInterface: typeof window.AndroidInterface !== 'undefined',
            userAgent: ua
        };
    }
    
    // 主测试函数
    function testContacts() {
        updateStatus('🔍 开始检测环境...', 'info');
        const env = detectEnvironment();
        
        let envInfo = \`
            <div style="margin-bottom: 10px;">
                <strong>📱 环境信息：</strong><br>
                • Android设备: \${env.isAndroid ? '✅' : '❌'}<br>
                • WebView环境: \${env.isWebView ? '✅' : '❌'}<br>
                • Android接口: \${env.hasAndroidInterface ? '✅' : '❌'}<br>
            </div>
        \`;
        
        updateResults(envInfo);
        
        if (!env.hasAndroidInterface) {
            updateStatus('⚠️ 未检测到AndroidInterface', 'warning');
            updateResults(envInfo + '<div style="color: #ffc107;">⚠️ 请确认在APP中打开此页面</div>');
            return;
        }
        
        // 检查权限
        try {
            if (typeof window.AndroidInterface.hasContactsPermission === 'function') {
                const hasPermission = window.AndroidInterface.hasContactsPermission();
                if (hasPermission) {
                    updateStatus('✅ 已有权限，读取通讯录...', 'success');
                    readContacts();
                } else {
                    updateStatus('📋 申请通讯录权限...', 'warning');
                    requestPermission();
                }
            } else {
                updateStatus('⚠️ 尝试直接读取...', 'warning');
                readContacts();
            }
        } catch (error) {
            updateStatus('❌ 测试失败: ' + error.message, 'error');
        }
    }
    
    // 申请权限
    function requestPermission() {
        try {
            if (typeof window.AndroidInterface.requestContactsPermission === 'function') {
                window.AndroidInterface.requestContactsPermission();
                updateStatus('📱 请在弹窗中授权...', 'info');
                
                window.onContactsPermissionResult = function(granted) {
                    if (granted) {
                        updateStatus('✅ 权限已授权！', 'success');
                        setTimeout(readContacts, 1000);
                    } else {
                        updateStatus('❌ 权限被拒绝', 'error');
                    }
                };
            }
        } catch (error) {
            updateStatus('❌ 权限申请失败: ' + error.message, 'error');
        }
    }
    
    // 读取通讯录
    function readContacts() {
        try {
            if (typeof window.AndroidInterface.getContacts === 'function') {
                updateStatus('📖 读取通讯录...', 'info');
                const contactsData = window.AndroidInterface.getContacts();
                
                if (contactsData) {
                    const contacts = JSON.parse(contactsData);
                    displayContacts(contacts);
                } else {
                    updateStatus('⚠️ 未获取到数据', 'warning');
                }
            } else {
                updateStatus('❌ 读取方法不可用', 'error');
            }
        } catch (error) {
            updateStatus('❌ 读取失败: ' + error.message, 'error');
        }
    }
    
    // 显示联系人
    function displayContacts(contacts) {
        if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
            updateStatus('📱 通讯录为空', 'info');
            return;
        }
        
        updateStatus(\`✅ 成功读取 \${contacts.length} 个联系人\`, 'success');
        
        let contactsHtml = \`
            <div style="margin-top: 15px;">
                <strong>📞 通讯录 (\${contacts.length}个)：</strong>
                <div style="max-height: 150px; overflow-y: auto;">
        \`;
        
        contacts.slice(0, 5).forEach(contact => {
            contactsHtml += \`
                <div style="padding: 5px; border-bottom: 1px solid #eee; font-size: 11px;">
                    <strong>\${contact.name || '未知'}</strong><br>
                    <span style="color: #666;">\${contact.phone || '无号码'}</span>
                </div>
            \`;
        });
        
        contactsHtml += \`
                </div>
                <div style="margin-top: 10px; padding: 8px; background: #d4edda; border-radius: 4px; color: #155724;">
                    ✅ 通讯录功能正常！
                </div>
            </div>
        \`;
        
        updateResults(document.getElementById('test-results').innerHTML + contactsHtml);
    }
    
    // 初始化
    createTestUI();
    updateStatus('📱 测试工具就绪', 'info');
})();
            `;
        }
        
        // 直接测试函数
        function loadAndTest() {
            try {
                eval(getTestCode());
            } catch (error) {
                alert('测试代码执行失败：' + error.message);
            }
        }
        
        // 复制代码到剪贴板
        function copyCode() {
            const code = getTestCode();
            navigator.clipboard.writeText(code).then(() => {
                alert('✅ 代码已复制到剪贴板！');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('✅ 代码已复制到剪贴板！');
            });
        }
        
        // 复制完整代码
        function copyFullCode() {
            fetch('./直接注入代码.js')
                .then(response => response.text())
                .then(code => {
                    navigator.clipboard.writeText(code).then(() => {
                        alert('✅ 完整代码已复制到剪贴板！');
                    });
                })
                .catch(() => {
                    // 如果无法加载文件，使用内联代码
                    copyCode();
                });
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否在APP环境中
            const isApp = typeof window.AndroidInterface !== 'undefined';
            const isAndroid = /Android/i.test(navigator.userAgent);
            
            if (isApp) {
                document.querySelector('.warning').innerHTML = 
                    '<strong>✅ 检测成功：</strong><br>当前正在APP环境中，可以进行通讯录功能测试。';
                document.querySelector('.warning').className = 'success';
            } else if (isAndroid) {
                document.querySelector('.warning').innerHTML = 
                    '<strong>⚠️ 注意：</strong><br>检测到Android设备，但未发现APP接口。请确保在随意花APP中打开此页面。';
            }
        });
    </script>
</body>
</html>
