@echo off
echo ========================================
echo 随意花APP - 专用构建工具 
echo 使用SDK路径: E:\SDK
echo ========================================
echo.

echo 正在设置Java环境...

REM 检查SDK目录中的JDK
set JAVA_FOUND=0

echo 检查 E:\SDK 中的JDK...

REM 检查常见的JDK位置
if exist "E:\SDK\jdk\bin\java.exe" (
    set "JAVA_HOME=E:\SDK\jdk"
    set JAVA_FOUND=1
    echo [找到] Java: %JAVA_HOME%
    goto setup_env
)

REM 检查带版本号的JDK目录
for /d %%i in ("E:\SDK\jdk*") do (
    if exist "%%i\bin\java.exe" (
        set "JAVA_HOME=%%i"
        set JAVA_FOUND=1
        echo [找到] Java: %%i
        goto setup_env
    )
)

REM 检查Android Studio JDK (如果SDK中没有)
if exist "C:\Program Files\Android\Android Studio\jbr\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Android\Android Studio\jbr"
    set JAVA_FOUND=1
    echo [找到] Android Studio JDK: %JAVA_HOME%
    goto setup_env
)

if exist "C:\Program Files (x86)\Android\Android Studio\jbr\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files (x86)\Android\Studio\jbr"
    set JAVA_FOUND=1
    echo [找到] Android Studio JDK: %JAVA_HOME%
    goto setup_env
)

REM 如果都没找到，手动输入
echo [未找到] 自动搜索失败
echo.
echo 请检查以下可能的JDK位置：
echo 1. E:\SDK\jdk
echo 2. E:\SDK\jdk-11 (或其他版本号)
echo 3. Android Studio安装目录\jbr
echo.
set /p JAVA_HOME="请输入完整JDK路径: "

if exist "%JAVA_HOME%\bin\java.exe" (
    set JAVA_FOUND=1
    echo [确认] Java路径有效
) else (
    echo [错误] 无效的Java路径: %JAVA_HOME%
    pause
    exit /b 1
)

:setup_env
echo.
echo 设置环境变量...
set "PATH=%JAVA_HOME%\bin;%PATH%"
set "ANDROID_HOME=E:\SDK"
set "ANDROID_SDK_ROOT=E:\SDK"

echo 当前环境设置：
echo JAVA_HOME = %JAVA_HOME%
echo ANDROID_HOME = %ANDROID_HOME%
echo.

echo 测试Java版本...
java -version
if %errorlevel% neq 0 (
    echo [错误] Java无法运行
    pause
    exit /b 1
)

echo.
echo ========================================
echo 开始构建随意花小贷APK
echo ========================================

echo 第1步: 清理项目缓存
if exist ".gradle" rmdir /s /q ".gradle"
if exist "app\build" rmdir /s /q "app\build"
echo [完成] 缓存清理

echo.
echo 第2步: Gradle清理
gradlew.bat clean
if %errorlevel% neq 0 (
    echo [错误] Gradle清理失败
    pause
    exit /b 1
)
echo [完成] Gradle清理

echo.
echo 第3步: 构建调试版APK
echo 请耐心等待，首次构建可能需要5-10分钟...
gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo [错误] APK构建失败
    echo.
    echo 可能的解决方案：
    echo 1. 检查网络连接（需要下载依赖）
    echo 2. 确认JDK版本兼容（推荐Java 11或17）
    echo 3. 清理后重试
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建成功！
echo ========================================

REM 处理生成的APK
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo [成功] 找到生成的APK文件
    
    REM 复制并重命名APK
    copy "app\build\outputs\apk\debug\app-debug.apk" "随意花小贷.apk" >nul
    echo [完成] APK已复制为: 随意花小贷.apk
    
    REM 显示文件信息
    for %%A in ("随意花小贷.apk") do (
        set /a size=%%~zA/1024/1024
        echo [信息] APK文件大小: !size! MB
    )
    
    echo.
    echo APK文件位置：
    echo - 原始位置: app\build\outputs\apk\debug\app-debug.apk
    echo - 便捷位置: %CD%\随意花小贷.apk
    
) else (
    echo [警告] 未找到生成的APK文件
    echo 请检查: app\build\outputs\apk\debug\ 目录
)

echo.
echo ========================================
echo 应用信息
echo ========================================
echo 应用名称: 随意花小贷
echo 包名: com.dailuanshej.loan  
echo 最低Android版本: 7.0 (API 24)
echo 目标Android版本: 14 (API 34)
echo 主要功能: WebView + 原生通讯录读取

echo.
echo 安装和使用说明：
echo 1. 将 "随意花小贷.apk" 传输到Android设备
echo 2. 在设备设置中启用"未知来源"应用安装
echo 3. 点击APK文件进行安装
echo 4. 启动应用后授予通讯录读取权限
echo 5. 应用将加载网站: https://dailuanshej.cn

echo.
echo 开发说明：
echo - 网页内容修改后，只需重启APP即可生效
echo - 无需重新打包APK，开发效率极高
echo - 通讯录功能通过JavaScript接口调用
echo - 后端继续使用现有的Web服务器

echo.
pause
