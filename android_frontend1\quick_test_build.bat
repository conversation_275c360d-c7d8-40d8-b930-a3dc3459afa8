@echo off
chcp 65001 >nul
echo ========================================
echo     随意花APP 快速测试编译工具
echo ========================================
echo.

cd /d "E:\仿随意花小贷源码完整版\android_frontend"

echo [1/4] 清理旧构建文件...
if exist "app\build\outputs\apk" (
    rmdir /s /q "app\build\outputs\apk"
    echo ✅ 清理完成
) else (
    echo ℹ️  无需清理
)

echo.
echo [2/4] 开始编译APK...
echo 📱 编译模式: Debug
echo 🎯 目标SDK: 34
echo ⚡ 最小SDK: 24

gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 编译成功！
    echo.
    echo [3/4] 查找生成的APK文件...
    
    for /r "app\build\outputs\apk" %%i in (*.apk) do (
        echo 📱 APK文件: %%i
        echo 📏 文件大小: 
        dir "%%i" | find "app-debug.apk"
        
        echo.
        echo [4/4] 复制APK到桌面...
        copy "%%i" "%USERPROFILE%\Desktop\随意花-测试版.apk" >nul
        if exist "%USERPROFILE%\Desktop\随意花-测试版.apk" (
            echo ✅ APK已复制到桌面: 随意花-测试版.apk
        ) else (
            echo ⚠️  复制失败，请手动复制APK文件
        )
        goto :install_prompt
    )
    
    echo ❌ 未找到APK文件
    goto :end
    
) else (
    echo.
    echo ❌ 编译失败！错误代码: %ERRORLEVEL%
    echo 💡 常见解决方案：
    echo    1. 检查Java环境变量
    echo    2. 清理并重新同步项目
    echo    3. 检查SDK路径配置
    goto :end
)

:install_prompt
echo.
echo ========================================
echo           🚀 安装和测试指南
echo ========================================
echo.
echo 📱 APK已生成完成，请按以下步骤测试通讯录功能：
echo.
echo 1️⃣  安装APK到手机：
echo    - 将桌面上的 "随意花-测试版.apk" 传输到手机
echo    - 在手机上安装（可能需要允许未知来源）
echo.
echo 2️⃣  测试通讯录功能：
echo    - 打开APP后，会加载网站
echo    - 在浏览器控制台输入: readAllContacts()
echo    - 或者访问测试页面进行详细测试
echo.
echo 3️⃣  查看测试页面：
echo    在APP中访问以下URL：
echo    📄 http://dailuanshej.cn/contacts_test.html
echo    (请确保测试页面已上传到服务器)
echo.
echo 4️⃣  观察测试结果：
echo    - 是否弹出权限请求对话框？
echo    - 授权后是否显示Toast提示？
echo    - 是否成功读取联系人数据？
echo    - 测试页面是否显示联系人列表？
echo.
echo 5️⃣  查看详细日志：
echo    连接手机到电脑，使用以下命令查看日志：
echo    adb logcat -s SuiYiHua
echo.

:end
echo.
echo ========================================
echo 按任意键退出...
pause >nul
