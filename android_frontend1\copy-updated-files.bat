@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 📱 复制今天更新的客户端文件
echo ========================================
echo.

echo 📂 目标文件夹: android_frontend1
echo 📂 源文件夹: android_frontend13
echo.

:: 创建assets文件夹（如果不存在）
if not exist "assets" mkdir "assets"

:: 复制今天更新的印章功能相关文件
echo 📋 复制客户端文件...

echo 1. 复制 customer_contract.php (印章功能合同页面)
copy "..\android_frontend13\assets\customer_contract.php" "assets\" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ customer_contract.php 复制成功
) else (
    echo ❌ customer_contract.php 复制失败
)

echo 2. 复制 customer_data.php (数据管理)
copy "..\android_frontend13\assets\CustomerData.php" "assets\" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ CustomerData.php 复制成功
) else (
    echo ❌ CustomerData.php 复制失败
)

echo 3. 复制 customer_view.php (客户查看)
copy "..\android_frontend13\assets\customer_view.php" "assets\" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ customer_view.php 复制成功
) else (
    echo ❌ customer_view.php 复制失败
)

echo 4. 复制 customers.json (客户数据)
copy "..\android_frontend13\assets\customers.json" "assets\" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ customers.json 复制成功
) else (
    echo ❌ customers.json 复制失败
)

echo 5. 复制移动端合同页面
copy "..\android_frontend13\assets\customer_contract_mobile.php" "assets\" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ customer_contract_mobile.php 复制成功
) else (
    echo ❌ customer_contract_mobile.php 复制失败
)

copy "..\android_frontend13\assets\customer_contract_mobile_h5.php" "assets\" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ customer_contract_mobile_h5.php 复制成功
) else (
    echo ❌ customer_contract_mobile_h5.php 复制失败
)

echo.
echo ========================================
echo 📋 文件复制完成
echo ========================================
echo.

echo 📱 今天更新的主要功能:
echo ✅ 印章编辑和显示功能
echo ✅ 管理员可编辑印章参数
echo ✅ 客户端可查看已应用的印章
echo ✅ 印章位置: right: 100px, top: -170px
echo ✅ 印章大小: 100×100px (默认)
echo ✅ 本地存储印章设置
echo ✅ SVG印章渲染
echo.

echo 🔴 印章功能说明:
echo - 管理员模式: customer_contract.php?id=1 (可编辑印章)
echo - 客户端模式: customer_contract.php?id=1&user=1 (只查看)
echo - 域名: https://dailuanshej.cn
echo.

echo 🚀 现在可以去生成APK了！
echo 这些文件包含了完整的印章功能支持。
echo.

pause
