@echo off
title Quick APK Build
color 0A
echo.
echo ==========================================
echo           快速APK编译工具
echo ==========================================
echo.

:: 设置工作目录
set PROJECT_DIR=E:\仿随意花小贷源码完整版\android_frontend
cd /d "%PROJECT_DIR%"

:: 检查环境
echo [Step 1] 检查环境...
if not exist gradlew.bat (
    echo ERROR: gradlew.bat not found!
    pause
    exit
)
echo OK: Gradle wrapper found.

:: 编译APK
echo.
echo [Step 2] 编译APK...
echo 正在编译，请稍候...
call gradlew.bat clean assembleDebug > build.log 2>&1

:: 检查结果
if %ERRORLEVEL% == 0 (
    echo SUCCESS: APK compiled successfully!
) else (
    echo ERROR: Build failed!
    echo Check build.log for details.
    pause
    exit
)

:: 检查APK文件
echo.
echo [Step 3] 检查APK文件...
set APK_FILE=app\build\outputs\apk\debug\app-debug.apk
if exist "%APK_FILE%" (
    echo SUCCESS: APK file created!
    echo Location: %CD%\%APK_FILE%
    echo.
    echo ===========================================
    echo                测试步骤
    echo ===========================================
    echo 1. 安装APK: adb install -r "%APK_FILE%"
    echo 2. 打开APP，访问: file:///android_asset/debug_test.html
    echo 3. 测试权限功能
    echo 4. 监控日志: adb logcat -s SuiYiHua
    echo ===========================================
) else (
    echo ERROR: APK file not found!
    echo Check build.log for details.
)

echo.
echo Press any key to continue...
pause > nul
