apply plugin: 'com.android.application'

android {
    compileSdk 33

    defaultConfig {
        applicationId "com.dailuanshej.loan"
        minSdk 24
        targetSdk 33
        versionCode 2
        versionName "1.0.1"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 应用描述信息
        manifestPlaceholders = [
            app_name: "优易花",
            app_description: "专业的金融服务应用",
            developer_name: "代乱设计"
        ]
    }

    // 签名配置
    signingConfigs {
        release {
            // 使用您的qianming签名文件
            storeFile file('qianming')  // 请确保qianming文件在app目录下
            storePassword 'your_password'  // 请替换为您的密码
            keyAlias 'your_alias'  // 请替换为您的别名
            keyPassword 'your_key_password'  // 请替换为您的密钥密码

            // 如果qianming文件在其他位置，请修改路径，例如：
            // storeFile file('../qianming')  // 如果在项目根目录
            // storeFile file('C:/path/to/qianming')  // 绝对路径
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // 启用签名配置
            signingConfig signingConfigs.release
        }
        debug {
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    buildFeatures {
        buildConfig true
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation 'com.google.android.material:material:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}
