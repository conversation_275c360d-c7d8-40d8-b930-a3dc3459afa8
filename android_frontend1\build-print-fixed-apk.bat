@echo off
echo ========================================
echo 🖨️ 构建打印修复版APK
echo ========================================

echo.
echo 📋 打印功能修复：
echo   ✅ 修复合同页面打印按钮点击问题
echo   ✅ 添加APK专用打印接口调用
echo   ✅ Web端和APK端兼容性处理
echo   ✅ 详细的打印调试日志
echo   ✅ 降级处理机制
echo.

echo 🔧 步骤1: 验证打印接口...
echo 检查MainActivity中的打印功能...

findstr /C:"printContract" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ APK打印接口存在
) else (
    echo ❌ APK打印接口缺失
    pause
    exit /b 1
)

findstr /C:"addJavascriptInterface.*Android" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ JavaScript接口已注册
) else (
    echo ❌ JavaScript接口未注册
    pause
    exit /b 1
)

echo.
echo 🔧 步骤2: 验证Web端修复...
echo 检查合同页面打印函数...

findstr /C:"Android.printContract" "..\源码\customer_contract_auto.php" >nul
if %errorlevel% equ 0 (
    echo ✅ Web端已调用APK打印接口
) else (
    echo ❌ Web端未调用APK打印接口
    pause
    exit /b 1
)

findstr /C:"function printContract" "..\源码\customer_contract_auto.php" >nul
if %errorlevel% equ 0 (
    echo ✅ 打印函数已添加
) else (
    echo ❌ 打印函数缺失
    pause
    exit /b 1
)

echo.
echo 🔧 步骤3: 清理构建环境...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 已清理构建缓存
)

echo.
echo 🔧 步骤4: 构建打印修复版APK...

call .\gradlew clean
if %errorlevel% neq 0 (
    echo ⚠️ Clean有警告，继续构建...
)

echo 正在构建打印修复版APK...
call .\gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    echo.
    echo 💡 请检查：
    echo   1. Java环境配置
    echo   2. Android SDK路径
    echo   3. 网络连接状态
    echo.
    pause
    exit /b 1
)

echo.
echo 📦 步骤5: 生成打印修复版APK...
set APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk
set APK_DEST=youyihua_print_fixed.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%"
    echo ✅ 打印修复版APK已生成: %APK_DEST%
    
    REM 显示文件信息
    for %%A in ("%APK_DEST%") do (
        set APK_SIZE=%%~zA
        set APK_DATE=%%~tA
    )
    echo   文件大小: %APK_SIZE% 字节
    echo   创建时间: %APK_DATE%
    
) else (
    echo ❌ 找不到构建的APK文件
    pause
    exit /b 1
)

echo.
echo 📤 步骤6: 部署APK...
if exist "..\源码\apk\" (
    copy "%APK_DEST%" "..\源码\apk\%APK_DEST%"
    echo ✅ APK已部署到服务器目录
)

echo.
echo ========================================
echo 🎉 打印修复版APK构建完成！
echo ========================================
echo.
echo 🖨️ 打印功能修复：
echo   📱 APK打印接口: Android.printContract()
echo   🌐 Web端兼容: 自动检测环境
echo   🔄 降级处理: APK失败时使用浏览器打印
echo   📋 调试日志: 详细的打印过程日志
echo   ⚡ 即时响应: 点击立即响应
echo.
echo 🧪 测试步骤：
echo   1. 安装打印修复版APK: %APK_DEST%
echo   2. 打开应用并登录
echo   3. 查看合同页面
echo   4. 点击"🖨️ 打印合同"按钮
echo   5. 验证打印功能是否正常响应
echo.
echo 🔍 预期效果：
echo   • 点击打印按钮立即响应
echo   • APK环境：调用系统打印服务
echo   • 显示打印预览或打印选项对话框
echo   • 支持保存为PDF或直接打印
echo   • 如果系统打印失败，显示替代选项
echo.
echo 📋 调试方法：
echo   • 查看APK日志: adb logcat | findstr "YouYiHua"
echo   • 检查JavaScript控制台输出
echo   • 验证打印接口调用
echo.
pause
