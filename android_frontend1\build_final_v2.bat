@echo off
chcp 65001 >nul
echo.
echo ===========================================
echo        优易花 APK - 最终构建脚本
echo ===========================================
echo.

:: 设置工作目录
cd /d "%~dp0"

:: 检查 gradlew.bat 是否存在
if not exist "gradlew.bat" (
    echo ❌ 错误: gradlew.bat 未在当前目录中找到。
    echo    请确保您在 android_frontend 文件夹中运行此脚本。
    pause
    exit /b 1
)

:: --- 步骤 1: 设置环境 ---
echo [1/3] 设置构建环境...
set "JAVA_HOME=E:\SDK\jdks\openjdk-11.0.12"
set "PATH=%JAVA_HOME%\bin;%PATH%"
echo ✅ JDK 已设置为: %JAVA_HOME%
echo.

:: --- 步骤 2: 执行构建 ---
echo [2/3] 正在构建 APK...
echo.

:: 清理旧的构建
call gradlew.bat clean
if %errorlevel% neq 0 (
    echo ❌ 清理失败，请检查错误信息。
    pause
    exit /b 1
)

:: 构建调试版 APK
call gradlew.bat assembleDebug --stacktrace
if %errorlevel% neq 0 (
    echo ❌ 构建失败，请检查上方的详细错误日志。
    pause
    exit /b 1
)
echo.
echo ✅ 构建成功!
echo.

:: --- 步骤 3: 复制并重命名 APK ---
echo [3/3] 正在复制并重命名 APK 文件...
set "APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk"
set "APK_DEST=..\优易花.apk"

if exist "%APK_SOURCE%" (
    copy /Y "%APK_SOURCE%" "%APK_DEST%" >nul
    if exist "%APK_DEST%" (
        echo.
        echo ==========================================
        echo 🎉 全部完成!
        echo.
        echo 📱 APK 文件已生成:
        echo    %CD%\%APK_DEST%
        echo ==========================================
    ) else (
        echo ⚠️ 警告: 复制 APK 文件失败。
        echo    源文件: %CD%\%APK_SOURCE%
    )
) else (
    echo ❌ 错误: 找不到生成的 APK 文件。
    echo    路径: %CD%\%APK_SOURCE%
)

echo.
echo 按任意键退出...
pause >nul
