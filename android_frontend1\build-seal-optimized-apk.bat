@echo off
echo ========================================
echo 🖼️ 构建印章优化版APK - 修复印章显示
echo ========================================

echo.
echo 📋 印章优化特性：
echo   ✅ 修复APK中印章被压缩的问题
echo   ✅ 优化印章显示尺寸 (120px → 100px移动端)
echo   ✅ 调整印章位置 (右侧180px → 150px移动端)
echo   ✅ 增加签名区域高度 (150px → 130px移动端)
echo   ✅ 保持Web端完整功能
echo   ✅ 移动端专用样式优化
echo.

echo 🔧 步骤1: 清理构建环境...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 已清理构建缓存
)

echo.
echo 🔧 步骤2: 验证印章优化代码...
echo 检查印章CSS优化是否已添加...

findstr /C:"lender-seal-img" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ 印章优化CSS已添加
) else (
    echo ❌ 印章优化CSS缺失
    pause
    exit /b 1
)

findstr /C:"max-width: 120px" "app\src\main\java\com\dailuanshej\loan\MainActivity.java" >nul
if %errorlevel% equ 0 (
    echo ✅ 印章尺寸优化已配置
) else (
    echo ❌ 印章尺寸优化缺失
    pause
    exit /b 1
)

echo.
echo 🔧 步骤3: 构建印章优化版APK...

call .\gradlew clean
if %errorlevel% neq 0 (
    echo ⚠️ Clean有警告，继续构建...
)

echo 正在构建印章优化版APK...
call .\gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    echo.
    echo 💡 请检查构建环境：
    echo   1. Java环境配置
    echo   2. Android SDK路径
    echo   3. 网络连接状态
    echo.
    pause
    exit /b 1
)

echo.
echo 📦 步骤4: 生成印章优化版APK...
set APK_SOURCE=app\build\outputs\apk\debug\app-debug.apk
set APK_DEST=youyihua_seal_optimized.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%"
    echo ✅ 印章优化版APK已生成: %APK_DEST%
    
    REM 显示文件信息
    for %%A in ("%APK_DEST%") do (
        set APK_SIZE=%%~zA
        set APK_DATE=%%~tA
    )
    echo   文件大小: %APK_SIZE% 字节
    echo   创建时间: %APK_DATE%
    
) else (
    echo ❌ 找不到构建的APK文件
    pause
    exit /b 1
)

echo.
echo 📤 步骤5: 部署APK...
if exist "..\源码\apk\" (
    copy "%APK_DEST%" "..\源码\apk\%APK_DEST%"
    echo ✅ APK已部署到服务器目录
)

echo.
echo ========================================
echo 🎉 印章优化版APK构建完成！
echo ========================================
echo.
echo 🖼️ 印章显示优化：
echo   📱 APK端印章尺寸: 120px (桌面) / 100px (移动端)
echo   📍 印章位置调整: 右侧180px (桌面) / 150px (移动端)
echo   📏 签名区域高度: 150px (桌面) / 130px (移动端)
echo   🔧 容器自适应: 最小高度80px，自动扩展
echo   📱 移动端优化: 专用CSS媒体查询
echo.
echo 🧪 测试重点：
echo   1. 安装印章优化版APK: %APK_DEST%
echo   2. 登录并查看合同页面
echo   3. 检查印章是否正常显示（不被压缩）
echo   4. 验证印章位置是否合适
echo   5. 测试打印功能是否正常
echo   6. 对比Web端显示效果
echo.
echo 🔍 印章显示检查项：
echo   ✅ 印章图片清晰，不模糊
echo   ✅ 印章尺寸适中，不被压缩
echo   ✅ 印章位置正确（合同右下角）
echo   ✅ 签名区域足够大
echo   ✅ 整体布局协调
echo.
echo 💡 如果印章仍有问题：
echo   • 检查上传的印章图片质量
echo   • 确认图片格式 (JPG/PNG)
echo   • 验证图片文件大小 (<2MB)
echo   • 对比手机浏览器显示效果
echo   • 查看APK控制台日志
echo.
pause
