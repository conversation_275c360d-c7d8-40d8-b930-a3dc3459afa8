@echo off
chcp 65001 >nul
echo.
echo ===========================================
echo    Android XML Error Diagnosis Tool
echo ===========================================
echo.

echo Xerces XML parsing error detected!
echo This usually indicates corrupted or malformed XML files.
echo.

echo [1/5] Checking common XML files for errors...
echo ================================================

:: Set working directory
cd /d "%~dp0"

:: Check if we're in the right directory
if not exist "app\src\main\AndroidManifest.xml" (
    echo ❌ AndroidManifest.xml not found
    echo Please run this script from the android_frontend directory
    pause
    exit /b 1
)

echo ✅ Found AndroidManifest.xml

:: Function to check XML file
echo [2/5] Validating critical XML files...
echo =====================================

set "XML_ERROR_FOUND="

:: Check AndroidManifest.xml
echo Checking AndroidManifest.xml...
powershell -Command "try { [xml]$xml = Get-Content 'app\src\main\AndroidManifest.xml'; echo 'OK' } catch { echo 'ERROR' }" > temp_result.txt
set /p result=<temp_result.txt
del temp_result.txt >nul 2>&1
if "%result%"=="ERROR" (
    echo ❌ AndroidManifest.xml has XML errors
    set "XML_ERROR_FOUND=1"
) else (
    echo ✅ AndroidManifest.xml is valid
)

:: Check strings.xml
echo Checking strings.xml...
if exist "app\src\main\res\values\strings.xml" (
    powershell -Command "try { [xml]$xml = Get-Content 'app\src\main\res\values\strings.xml'; echo 'OK' } catch { echo 'ERROR' }" > temp_result.txt
    set /p result=<temp_result.txt
    del temp_result.txt >nul 2>&1
    if "%result%"=="ERROR" (
        echo ❌ strings.xml has XML errors
        set "XML_ERROR_FOUND=1"
    ) else (
        echo ✅ strings.xml is valid
    )
) else (
    echo ⚠️  strings.xml not found
)

:: Check colors.xml
echo Checking colors.xml...
if exist "app\src\main\res\values\colors.xml" (
    powershell -Command "try { [xml]$xml = Get-Content 'app\src\main\res\values\colors.xml'; echo 'OK' } catch { echo 'ERROR' }" > temp_result.txt
    set /p result=<temp_result.txt
    del temp_result.txt >nul 2>&1
    if "%result%"=="ERROR" (
        echo ❌ colors.xml has XML errors
        set "XML_ERROR_FOUND=1"
    ) else (
        echo ✅ colors.xml is valid
    )
) else (
    echo ⚠️  colors.xml not found
)

:: Check build.gradle files
echo [3/5] Checking Gradle files...
echo ==============================

if exist "build.gradle" (
    echo ✅ Root build.gradle found
) else (
    echo ❌ Root build.gradle missing
)

if exist "app\build.gradle" (
    echo ✅ App build.gradle found
) else (
    echo ❌ App build.gradle missing
)

echo [4/5] Generating fixes...
echo =========================

if defined XML_ERROR_FOUND (
    echo ❌ XML errors detected! Creating fixed files...
    goto fix_xml_files
) else (
    echo ✅ No XML syntax errors found
    echo.
    echo The Xerces error might be caused by:
    echo 1. Gradle build configuration issues
    echo 2. Corrupted build cache
    echo 3. Encoding problems
    echo.
    echo Recommended actions:
    echo 1. Clean project: gradlew clean
    echo 2. Invalidate caches in Android Studio
    echo 3. Re-sync project
    goto other_fixes
)

:fix_xml_files
echo.
echo Creating backup and fixed XML files...

:: Backup original AndroidManifest.xml
if exist "app\src\main\AndroidManifest.xml" (
    copy "app\src\main\AndroidManifest.xml" "app\src\main\AndroidManifest.xml.backup" >nul
)

:: Create clean AndroidManifest.xml
echo Creating clean AndroidManifest.xml...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<manifest xmlns:android="http://schemas.android.com/apk/res/android"
echo     xmlns:tools="http://schemas.android.com/tools"
echo     package="com.dailuanshej.loan"^>
echo.
echo     ^<uses-permission android:name="android.permission.INTERNET" /^>
echo     ^<uses-permission android:name="android.permission.READ_CONTACTS" /^>
echo     ^<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /^>
echo     ^<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /^>
echo     ^<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /^>
echo.
echo     ^<application
echo         android:allowBackup="true"
echo         android:icon="@mipmap/ic_launcher"
echo         android:label="@string/app_name"
echo         android:theme="@style/AppTheme"
echo         android:usesCleartextTraffic="true"
echo         tools:targetApi="28"^>
echo.
echo         ^<activity
echo             android:name=".MainActivity"
echo             android:exported="true"
echo             android:screenOrientation="portrait"^>
echo             ^<intent-filter^>
echo                 ^<action android:name="android.intent.action.MAIN" /^>
echo                 ^<category android:name="android.intent.category.LAUNCHER" /^>
echo             ^</intent-filter^>
echo         ^</activity^>
echo.
echo     ^</application^>
echo.
echo ^</manifest^>
) > "app\src\main\AndroidManifest.xml"

:: Create clean strings.xml
echo Creating clean strings.xml...
if not exist "app\src\main\res\values" mkdir "app\src\main\res\values"
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<resources^>
echo     ^<string name="app_name"^>随意花小贷^</string^>
echo     ^<string name="welcome_message"^>欢迎使用随意花小贷^</string^>
echo     ^<string name="loading"^>加载中...^</string^>
echo     ^<string name="network_error"^>网络连接错误^</string^>
echo     ^<string name="retry"^>重试^</string^>
echo ^</resources^>
) > "app\src\main\res\values\strings.xml"

:: Create clean colors.xml
echo Creating clean colors.xml...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<resources^>
echo     ^<color name="colorPrimary"^>#2196F3^</color^>
echo     ^<color name="colorPrimaryDark"^>#1976D2^</color^>
echo     ^<color name="colorAccent"^>#FF4081^</color^>
echo     ^<color name="white"^>#FFFFFF^</color^>
echo     ^<color name="black"^>#000000^</color^>
echo ^</resources^>
) > "app\src\main\res\values\colors.xml"

echo ✅ Fixed XML files created!

:other_fixes
echo.
echo [5/5] Additional fixes for Xerces errors...
echo ==========================================

echo Creating Android Studio project refresh script...
(
echo @echo off
echo echo Refreshing Android Studio project...
echo echo.
echo echo Step 1: Cleaning build cache...
echo if exist ".gradle" rmdir /s /q ".gradle"
echo if exist "app\build" rmdir /s /q "app\build"
echo if exist "build" rmdir /s /q "build"
echo.
echo echo Step 2: Refreshing Gradle wrapper...
echo if exist "gradle\wrapper\gradle-wrapper.jar" ^(
echo     echo Gradle wrapper found
echo ^) else ^(
echo     echo Downloading Gradle wrapper...
echo     gradlew wrapper
echo ^)
echo.
echo echo ✅ Project refreshed!
echo echo.
echo echo Next steps in Android Studio:
echo echo 1. File ^> Invalidate Caches and Restart
echo echo 2. File ^> Sync Project with Gradle Files
echo echo 3. Build ^> Clean Project
echo echo 4. Build ^> Rebuild Project
echo echo.
echo pause
) > "refresh_android_studio.bat"

echo.
echo ✅ Diagnosis complete!
echo =====================================
echo.
if defined XML_ERROR_FOUND (
    echo Fixed files created:
    echo - AndroidManifest.xml ^(backup saved^)
    echo - strings.xml
    echo - colors.xml
    echo.
)
echo Additional tools created:
echo - refresh_android_studio.bat
echo.
echo 📋 Next steps:
echo 1. Open Android Studio
echo 2. File ^> Invalidate Caches and Restart
echo 3. File ^> Sync Project with Gradle Files
echo 4. Try building again
echo.
echo If errors persist:
echo 1. Check Android Studio Event Log for details
echo 2. Run: refresh_android_studio.bat
echo 3. Verify SDK path in Project Structure
echo.
echo =====================================
pause
