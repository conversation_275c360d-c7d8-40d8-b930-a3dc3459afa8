@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 📱 复制APK到服务器目录
echo ========================================
echo.

echo 🎯 目标：将生成的APK复制到源码/apk/目录
echo 📂 源文件：android_frontend1/app/build/outputs/apk/debug/app-debug.apk
echo 📂 目标文件：源码/apk/youyihua_v1.0.1.apk
echo.

:: 检查源APK文件是否存在
if not exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ❌ 源APK文件不存在: app\build\outputs\apk\debug\app-debug.apk
    echo 💡 请先生成APK文件
    pause
    exit /b 1
)

echo ✅ 找到源APK文件

:: 显示文件信息
echo.
echo 📋 源APK文件信息:
for %%I in ("app\build\outputs\apk\debug\app-debug.apk") do (
    echo    文件大小: %%~zI 字节
    echo    修改时间: %%~tI
)

:: 检查目标目录
if not exist "..\源码\apk" (
    echo ❌ 目标目录不存在: ..\源码\apk
    echo 💡 请确认源码目录结构
    pause
    exit /b 1
)

echo ✅ 目标目录存在

:: 备份现有APK文件（如果存在）
if exist "..\源码\apk\youyihua_v1.0.1.apk" (
    echo 🔄 备份现有APK文件...
    copy "..\源码\apk\youyihua_v1.0.1.apk" "..\源码\apk\youyihua_v1.0.1.apk.backup.%date:~0,4%%date:~5,2%%date:~8,2%" >nul
    if %errorlevel% equ 0 (
        echo ✅ 现有APK已备份
    ) else (
        echo ⚠️  备份失败，继续复制
    )
)

:: 复制新APK文件
echo.
echo 📱 复制新APK文件...
copy "app\build\outputs\apk\debug\app-debug.apk" "..\源码\apk\youyihua_v1.0.1.apk"

if %errorlevel% equ 0 (
    echo ✅ APK文件复制成功！
    
    :: 显示新文件信息
    echo.
    echo 📋 新APK文件信息:
    for %%I in ("..\源码\apk\youyihua_v1.0.1.apk") do (
        echo    文件路径: ..\源码\apk\youyihua_v1.0.1.apk
        echo    文件大小: %%~zI 字节 (%.2f MB)
        echo    修改时间: %%~tI
    )
    
    :: 计算文件大小（MB）
    for %%I in ("..\源码\apk\youyihua_v1.0.1.apk") do (
        set /a size_mb=%%~zI/1024/1024
    )
    
    echo.
    echo 🌐 下载链接信息:
    echo    网页链接: https://dailuanshej.cn/apk/youyihua_v1.0.1.apk
    echo    文件大小: 约 %size_mb% MB
    echo    版本信息: v1.1 (支持印章功能)
    echo.
    
    echo 🔴 印章功能确认:
    echo    ✅ 管理员可编辑印章
    echo    ✅ 客户端可查看印章
    echo    ✅ 印章位置: right:100px, top:-170px
    echo    ✅ 域名: https://dailuanshej.cn
    echo.
    
    echo 📱 安装测试建议:
    echo    1. 直接传输APK到手机测试安装
    echo    2. 通过 https://dailuanshej.cn/download.php 测试下载
    echo    3. 验证印章功能是否正常
    echo.
    
    echo ========================================
    echo 🎉 APK文件更新完成！
    echo ========================================
    
) else (
    echo ❌ APK文件复制失败
    echo 💡 请检查文件权限和路径
)

echo.
pause
