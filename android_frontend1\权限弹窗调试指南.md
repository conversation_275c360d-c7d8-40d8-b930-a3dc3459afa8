# 🔧 权限弹窗无响应调试指南

## 问题现象
- 点击权限按钮后没有弹窗出现
- 或者弹窗出现但点击无响应
- 权限申请流程卡住

## 调试步骤

### 1. 首先使用调试页面
1. 将新编译的APK安装到手机
2. 在APP中访问: `file:///android_asset/权限调试测试页面.html`
3. 按顺序点击测试按钮，观察日志输出

### 2. 查看ADB日志
连接手机后运行：
```bash
adb logcat -s SuiYiHua
```
观察权限申请过程的详细日志。

### 3. 检查可能的问题

#### 问题1: Activity状态异常
**现象**: 日志显示"Activity已销毁"或"正在结束"
**解决**: 确保在Activity正常状态下申请权限

#### 问题2: SDK版本问题
**现象**: SDK < 23 但权限处理异常
**解决**: 检查targetSdkVersion设置

#### 问题3: 权限声明缺失
**检查**: AndroidManifest.xml中是否正确声明
```xml
<uses-permission android:name="android.permission.READ_CONTACTS" />
```

#### 问题4: 系统权限设置
**检查**: 手机系统设置中APP权限状态
- 进入设置 > 应用管理 > 你的APP > 权限管理
- 查看通讯录权限状态

### 4. 增强调试方法

#### 使用新增的调试方法:
1. `forcePermissionDialog()` - 强制权限弹窗测试
2. `getDetailedStatus()` - 获取详细状态信息
3. 查看实时日志输出

#### 权限申请流程检查:
```
1. checkSelfPermission() -> 检查当前状态
2. shouldShowRequestPermissionRationale() -> 检查是否需要说明
3. requestPermissions() -> 发送权限请求
4. onRequestPermissionsResult() -> 处理权限结果
```

### 5. 常见解决方案

#### 方案1: 手动触发权限设置
如果弹窗无响应，引导用户手动设置：
```java
Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
intent.setData(Uri.parse("package:" + getPackageName()));
startActivity(intent);
```

#### 方案2: 检查WebView设置
确保WebView可以正常调用原生方法：
```java
webSettings.setJavaScriptEnabled(true);
webView.addJavascriptInterface(new ContactsJSInterface(), "AndroidContacts");
```

#### 方案3: 权限组问题
某些手机可能需要申请整个权限组：
```java
new String[]{
    Manifest.permission.READ_CONTACTS,
    Manifest.permission.WRITE_CONTACTS
}
```

### 6. 测试不同场景

#### 场景1: 首次安装
- 清除APP数据
- 重新安装APK
- 测试首次权限申请

#### 场景2: 权限被拒绝后
- 先拒绝权限
- 再次申请
- 查看是否有"不再询问"选项

#### 场景3: 权限永久拒绝
- 勾选"不再询问"并拒绝
- 测试引导到设置页面

### 7. 调试命令

#### 查看权限状态:
```bash
adb shell dumpsys package com.dailuanshej.loan | grep permission
```

#### 重置权限:
```bash
adb shell pm reset-permissions com.dailuanshej.loan
```

#### 手动授权:
```bash
adb shell pm grant com.dailuanshej.loan android.permission.READ_CONTACTS
```

## 预期结果
调试成功后应该看到：
1. 权限弹窗正常显示
2. 点击"允许"后权限正确授予
3. 通讯录可以正常读取
4. JS接口正常工作

## 如果仍然无法解决
请提供以下信息：
1. 手机型号和Android版本
2. ADB logcat日志输出
3. 调试页面的详细状态信息
4. 具体的错误现象描述
