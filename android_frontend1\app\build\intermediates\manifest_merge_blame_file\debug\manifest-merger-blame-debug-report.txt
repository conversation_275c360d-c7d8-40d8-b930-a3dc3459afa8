1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dailuanshej.loan.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:6:5-79
12-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.READ_CONTACTS" />
13-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:7:5-72
13-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:7:22-69
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:8:5-81
14-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:9:5-80
15-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:9:22-77
16
17    <application
17-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:11:5-29:19
18        android:allowBackup="true"
18-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:12:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.8.0] E:\SDK\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
20        android:debuggable="true"
21        android:extractNativeLibs="false"
22        android:icon="@drawable/ic_launcher"
22-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:13:9-45
23        android:label="@string/app_name"
23-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:15:9-41
24        android:networkSecurityConfig="@xml/network_security_config"
24-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:18:9-69
25        android:roundIcon="@mipmap/ic_launcher_round"
25-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:14:9-54
26        android:theme="@style/AppTheme"
26-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:16:9-40
27        android:usesCleartextTraffic="true" >
27-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:17:9-44
28        <activity
28-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:20:9-28:20
29            android:name="com.dailuanshej.loan.MainActivity"
29-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:21:13-41
30            android:exported="true"
30-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:22:13-36
31            android:screenOrientation="portrait" >
31-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:23:13-49
32            <intent-filter>
32-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:24:13-27:29
33                <action android:name="android.intent.action.MAIN" />
33-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:25:17-69
33-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:25:25-66
34
35                <category android:name="android.intent.category.LAUNCHER" />
35-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:26:17-77
35-->E:\android_frontend\android_frontend\app\src\main\AndroidManifest.xml:26:27-74
36            </intent-filter>
37        </activity>
38
39        <provider
39-->[androidx.emoji2:emoji2:1.2.0] E:\SDK\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
40            android:name="androidx.startup.InitializationProvider"
40-->[androidx.emoji2:emoji2:1.2.0] E:\SDK\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
41            android:authorities="com.dailuanshej.loan.debug.androidx-startup"
41-->[androidx.emoji2:emoji2:1.2.0] E:\SDK\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
42            android:exported="false" >
42-->[androidx.emoji2:emoji2:1.2.0] E:\SDK\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
43            <meta-data
43-->[androidx.emoji2:emoji2:1.2.0] E:\SDK\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
44                android:name="androidx.emoji2.text.EmojiCompatInitializer"
44-->[androidx.emoji2:emoji2:1.2.0] E:\SDK\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
45                android:value="androidx.startup" />
45-->[androidx.emoji2:emoji2:1.2.0] E:\SDK\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
46            <meta-data
46-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\SDK\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
47                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
47-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\SDK\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
48                android:value="androidx.startup" />
48-->[androidx.lifecycle:lifecycle-process:2.4.1] E:\SDK\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
49        </provider>
50    </application>
51
52</manifest>
