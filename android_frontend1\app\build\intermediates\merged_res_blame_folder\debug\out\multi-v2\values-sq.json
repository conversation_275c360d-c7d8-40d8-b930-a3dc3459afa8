{"logs": [{"outputFile": "com.dailuanshej.loan.app-mergeDebugResources-27:/values-sq/values-sq.xml", "map": [{"source": "E:\\SDK\\caches\\transforms-3\\ae146d59d58c77d62f24ab479ef1acb9\\transformed\\appcompat-1.5.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,7613", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,7690"}}, {"source": "E:\\SDK\\caches\\transforms-3\\7cfde09bef56db81a739e41054cc6284\\transformed\\material-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,353,454,580,663,728,828,898,957,1055,1119,1178,1250,1313,1367,1484,1541,1603,1657,1729,1864,1947,2025,2166,2250,2332,2422,2475,2534,2600,2671,2750,2838,2914,2992,3064,3137,3226,3298,3392,3491,3565,3637,3738,3788,3854,3944,4033,4095,4159,4222,4338,4446,4555,4664,4721,4784", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,89,52,58,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,56,62,82", "endOffsets": "262,348,449,575,658,723,823,893,952,1050,1114,1173,1245,1308,1362,1479,1536,1598,1652,1724,1859,1942,2020,2161,2245,2327,2417,2470,2529,2595,2666,2745,2833,2909,2987,3059,3132,3221,3293,3387,3486,3560,3632,3733,3783,3849,3939,4028,4090,4154,4217,4333,4441,4550,4659,4716,4779,4862"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3013,3099,3200,3326,3409,3474,3574,3644,3703,3801,3865,3924,3996,4059,4113,4230,4287,4349,4403,4475,4610,4693,4771,4912,4996,5078,5168,5221,5280,5346,5417,5496,5584,5660,5738,5810,5883,5972,6044,6138,6237,6311,6383,6484,6534,6600,6690,6779,6841,6905,6968,7084,7192,7301,7410,7467,7530", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,89,52,58,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,56,62,82", "endOffsets": "312,3094,3195,3321,3404,3469,3569,3639,3698,3796,3860,3919,3991,4054,4108,4225,4282,4344,4398,4470,4605,4688,4766,4907,4991,5073,5163,5216,5275,5341,5412,5491,5579,5655,5733,5805,5878,5967,6039,6133,6232,6306,6378,6479,6529,6595,6685,6774,6836,6900,6963,7079,7187,7296,7405,7462,7525,7608"}}, {"source": "E:\\SDK\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "7695", "endColumns": "100", "endOffsets": "7791"}}]}]}