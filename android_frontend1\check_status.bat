@echo off
echo ========================================
echo        项目状态检查
echo ========================================
echo.

cd /d "E:\仿随意花小贷源码完整版\android_frontend"

echo [Java代码] MainActivity.java
if exist "app\src\main\java\com\dailuanshej\loan\MainActivity.java" (
    echo ✅ 存在 - 权限修复已完成
) else (
    echo ❌ 缺失
)

echo.
echo [调试文件] Assets目录
if exist "app\src\main\assets\debug_test.html" (
    echo ✅ debug_test.html - 调试页面
) else (
    echo ❌ debug_test.html 缺失
)

if exist "app\src\main\assets\test.html" (
    echo ✅ test.html - 简化测试页面
) else (
    echo ❌ test.html 缺失
)

if exist "app\src\main\assets\contacts_bridge.js" (
    echo ✅ contacts_bridge.js - JS桥接
) else (
    echo ❌ contacts_bridge.js 缺失
)

echo.
echo [编译环境]
if exist "gradlew.bat" (
    echo ✅ gradlew.bat - Gradle包装器
) else (
    echo ❌ gradlew.bat 缺失
)

if exist "build.gradle" (
    echo ✅ build.gradle - 项目配置
) else (
    echo ❌ build.gradle 缺失
)

echo.
echo [APK文件]
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ app-debug.apk 存在
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do echo    大小: %%~zA 字节
) else (
    echo ❌ APK未生成，需要编译
)

echo.
echo [编译脚本]
if exist "quick_build.bat" (
    echo ✅ quick_build.bat - 快速编译
) else (
    echo ❌ quick_build.bat 缺失
)

if exist "compile_simple.bat" (
    echo ✅ compile_simple.bat - 简单编译
) else (
    echo ❌ compile_simple.bat 缺失
)

echo.
echo ========================================
echo 下一步操作：
echo 1. 运行 quick_build.bat 编译APK
echo 2. 安装APK到手机测试
echo 3. 访问 debug_test.html 测试权限
echo ========================================
pause
