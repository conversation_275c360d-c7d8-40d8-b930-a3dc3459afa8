<?php
/**
 * 客户管理页面 - 模拟原始ThinkPHP功能
 */

session_start();
require_once 'customer_data.php';

// 简单的登录检查（测试模式跳过）
if (!isset($_GET['test']) && (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true)) {
    header('Location: admin.php');
    exit;
}

$customerDB = new CustomerData();
$message = '';
$message_type = '';

// 处理删除请求
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $deleteId = intval($_GET['id']);
    if ($customerDB->deleteCustomer($deleteId)) {
        $message = '客户删除成功！';
        $message_type = 'success';
    } else {
        $message = '客户删除失败！';
        $message_type = 'error';
    }
}

// 处理搜索
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
if ($keyword) {
    $customers = $customerDB->searchCustomers($keyword);
} else {
    $customers = $customerDB->getAllCustomers();
}

// 获取统计信息
$stats = $customerDB->getStatistics();

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>客户管理 - 小贷系统</title>
    <link href="./Public/main/css/layui.css" rel="stylesheet" type="text/css">
    <style>
        body { margin: 0; font-family: Arial, sans-serif; background: #f5f5f5; }
        .admin-header { background: #393D49; color: white; height: 60px; line-height: 60px; padding: 0 20px; }
        .admin-main { padding: 20px; }
        .breadcrumb { background: white; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .content-box { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .search-box { margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #009688; color: white; }
        .btn-success { background: #5FB878; color: white; }
        .btn-danger { background: #FF5722; color: white; }
        .btn-info { background: #01AAED; color: white; }
        .btn-warning { background: #FFB800; color: white; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e6e6e6; }
        th { background: #f2f2f2; font-weight: bold; }
        tr:hover { background: #f8f9fa; }
        .status-approved { color: #5FB878; }
        .status-pending { color: #FFB800; }
        .status-completed { color: #009688; }
        .status-overdue { color: #FF5722; }
        .pagination { margin-top: 20px; text-align: center; }
        .pagination a { padding: 8px 12px; margin: 0 2px; background: #f2f2f2; color: #333; text-decoration: none; border-radius: 3px; }
        .pagination a:hover { background: #009688; color: white; }
        .pagination .current { padding: 8px 12px; margin: 0 2px; background: #009688; color: white; border-radius: 3px; }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <span>🏠 小贷系统 - 后台管理</span>
        <span style="float: right;">
            <a href="admin.php" style="color: #bdc3c7; margin-left: 20px;">🔙 返回首页</a>
            <a href="admin.php?logout=1" style="color: #bdc3c7; margin-left: 20px;">🚪 退出</a>
        </span>
    </div>

    <!-- 主要内容区域 -->
    <div class="admin-main">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <span>当前位置：</span>
            <a href="admin.php">管理首页</a> > 
            <span>客户管理</span>
        </div>

        <!-- 内容区域 -->
        <div class="content-box">
            <h2>💰 客户管理</h2>

            <?php if ($message): ?>
            <div class="message <?php echo $message_type; ?>" style="padding: 15px; border-radius: 5px; margin-bottom: 20px; <?php echo $message_type == 'success' ? 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>
            
            <!-- 搜索区域 -->
            <div class="search-box">
                <form method="get" style="display: inline-block;">
                    <?php if (isset($_GET['test'])): ?>
                    <input type="hidden" name="test" value="1">
                    <?php endif; ?>
                    <input type="text" name="keyword" value="<?php echo htmlspecialchars($keyword); ?>" placeholder="请输入客户姓名、手机号或身份证号" style="padding: 8px; border: 1px solid #ddd; border-radius: 3px; width: 250px;">
                    <button type="submit" class="btn btn-primary">🔍 搜索</button>
                    <a href="customer_management.php<?php echo isset($_GET['test']) ? '?test=1' : ''; ?>" class="btn btn-info">🔄 重置</a>
                </form>
                
                <div style="float: right;">
                    <a href="customer_add.php<?php echo isset($_GET['test']) ? '?test=1' : ''; ?>" class="btn btn-success">➕ 添加客户</a>
                    <a href="#" class="btn btn-info" onclick="alert('导出功能开发中...')">📊 导出数据</a>
                    <a href="#" class="btn btn-warning" onclick="alert('批量操作功能开发中...')">📋 批量操作</a>
                </div>
                <div style="clear: both;"></div>
            </div>

            <!-- 数据表格 -->
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>客户姓名</th>
                        <th>手机号</th>
                        <th>身份证号</th>
                        <th>借款金额</th>
                        <th>借款分期</th>
                        <th>借款状态</th>
                        <th>借款期限</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($customers as $customer): ?>
                    <tr>
                        <td><?php echo $customer['id']; ?></td>
                        <td><?php echo $customer['customer_name']; ?></td>
                        <td><?php echo $customer['phone']; ?></td>
                        <td><?php echo substr($customer['id_card'], 0, 6) . '****' . substr($customer['id_card'], -4); ?></td>
                        <td>¥<?php echo number_format($customer['loan_amount']); ?></td>
                        <td><?php echo $customer['loan_periods']; ?>期 (<?php echo $customer['loan_periods']; ?>个月)</td>
                        <td>
                            <?php
                            $status_class = '';
                            switch($customer['loan_status']) {
                                case '已放款': $status_class = 'status-approved'; break;
                                case '审核中': $status_class = 'status-pending'; break;
                                case '已还款': $status_class = 'status-completed'; break;
                                case '逾期': $status_class = 'status-overdue'; break;
                            }
                            ?>
                            <span class="<?php echo $status_class; ?>">● <?php echo $customer['loan_status']; ?></span>
                        </td>
                        <td>
                            <?php if (isset($customer['loan_start_date']) && isset($customer['loan_end_date']) && $customer['loan_start_date'] && $customer['loan_end_date']): ?>
                                <?php echo $customer['loan_start_date']; ?> 至 <?php echo $customer['loan_end_date']; ?>
                            <?php else: ?>
                                <span style="color: #999;">待设定</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $customer['created_time']; ?></td>
                        <td>
                            <a href="customer_view.php?id=<?php echo $customer['id']; ?><?php echo isset($_GET['test']) ? '&test=1' : ''; ?>" class="btn btn-info" style="padding: 4px 8px; font-size: 12px;">👁️ 查看</a>
                            <a href="customer_edit.php?id=<?php echo $customer['id']; ?><?php echo isset($_GET['test']) ? '&test=1' : ''; ?>" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">✏️ 编辑</a>
                            <a href="customer_contract_auto.php?id=<?php echo $customer['id']; ?><?php echo isset($_GET['test']) ? '&test=1' : ''; ?>" class="btn btn-warning" style="padding: 4px 8px; font-size: 12px;">📄 合同</a>
                            <a href="customer_management.php?action=delete&id=<?php echo $customer['id']; ?><?php echo isset($_GET['test']) ? '&test=1' : ''; ?>" class="btn btn-danger" style="padding: 4px 8px; font-size: 12px;" onclick="return confirm('确定要删除客户【<?php echo htmlspecialchars($customer['customer_name']); ?>】吗？\n\n删除后无法恢复！')">🗑️ 删除</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <!-- 分页 -->
            <div class="pagination">
                <span>共 <?php echo count($customers); ?> 条记录 | 第 1 / 1 页</span>
                <?php if ($keyword): ?>
                <span style="margin-left: 20px;">搜索关键词: "<?php echo htmlspecialchars($keyword); ?>"</span>
                <?php endif; ?>
            </div>

            <!-- 统计信息 -->
            <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h4>📊 客户统计</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #5FB878; font-weight: bold;"><?php echo $stats['approved']; ?></div>
                        <div>已放款客户</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #FFB800; font-weight: bold;"><?php echo $stats['pending']; ?></div>
                        <div>审核中客户</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #009688; font-weight: bold;"><?php echo $stats['completed']; ?></div>
                        <div>已还款客户</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #FF5722; font-weight: bold;"><?php echo $stats['overdue']; ?></div>
                        <div>逾期客户</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #007bff; font-weight: bold;"><?php echo $stats['total']; ?></div>
                        <div>总客户数</div>
                    </div>
                </div>
            </div>

            <!-- 功能说明 -->
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h4>📋 功能说明</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>查看客户：</strong> 查看客户详细信息、借款记录和还款情况</li>
                    <li><strong>编辑客户：</strong> 修改客户基本信息和借款参数</li>
                    <li><strong>查看合同：</strong> 查看和下载借款合同</li>
                    <li><strong>删除客户：</strong> 删除客户记录（谨慎操作）</li>
                    <li><strong>搜索功能：</strong> 支持按姓名、手机号、身份证号搜索</li>
                    <li><strong>数据导出：</strong> 导出客户列表为Excel文件</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
