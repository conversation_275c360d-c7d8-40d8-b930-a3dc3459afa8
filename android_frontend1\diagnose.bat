@echo off
chcp 65001 >nul
echo 🔍 Android 构建问题诊断工具
echo ================================
echo.

echo 第1步：检查基本信息
echo Java版本：
java -version
echo.

echo 第2步：检查Gradle版本
gradlew.bat --version
echo.

echo 第3步：检查项目结构
echo 项目文件：
dir /b
echo.
echo App目录：
dir app /b
echo.

echo 第4步：清理项目缓存
echo 正在清理...
if exist ".gradle" (
    rmdir /s /q ".gradle"
    echo ✅ 删除 .gradle 目录
)
if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ 删除 app\build 目录
)
echo.

echo 第5步：运行详细构建诊断
echo ================================
echo 🔍 运行 gradlew clean --stacktrace --info
echo.
gradlew.bat clean --stacktrace --info

echo.
echo 第6步：尝试构建APK
echo ================================
echo 🔍 运行 gradlew assembleDebug --stacktrace --info
echo.
gradlew.bat assembleDebug --stacktrace --info

echo.
echo 诊断完成，请查看上面的详细错误信息
pause
