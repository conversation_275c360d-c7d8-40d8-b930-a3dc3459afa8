{"logs": [{"outputFile": "com.dailuanshej.loan.app-mergeDebugResources-27:/values-hi/values-hi.xml", "map": [{"source": "E:\\SDK\\caches\\transforms-3\\7cfde09bef56db81a739e41054cc6284\\transformed\\material-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,340,447,579,662,727,821,890,949,1034,1097,1155,1220,1281,1342,1448,1506,1566,1625,1695,1811,1890,1970,2104,2179,2255,2352,2409,2464,2530,2600,2677,2763,2831,2907,2988,3066,3152,3239,3336,3435,3509,3579,3683,3737,3804,3894,3986,4048,4112,4175,4280,4388,4489,4598,4659,4718", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,80,106,131,82,64,93,68,58,84,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,96,56,54,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,60,58,78", "endOffsets": "254,335,442,574,657,722,816,885,944,1029,1092,1150,1215,1276,1337,1443,1501,1561,1620,1690,1806,1885,1965,2099,2174,2250,2347,2404,2459,2525,2595,2672,2758,2826,2902,2983,3061,3147,3234,3331,3430,3504,3574,3678,3732,3799,3889,3981,4043,4107,4170,4275,4383,4484,4593,4654,4713,4792"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2991,3072,3179,3311,3394,3459,3553,3622,3681,3766,3829,3887,3952,4013,4074,4180,4238,4298,4357,4427,4543,4622,4702,4836,4911,4987,5084,5141,5196,5262,5332,5409,5495,5563,5639,5720,5798,5884,5971,6068,6167,6241,6311,6415,6469,6536,6626,6718,6780,6844,6907,7012,7120,7221,7330,7391,7450", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "endColumns": "12,80,106,131,82,64,93,68,58,84,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,96,56,54,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,60,58,78", "endOffsets": "304,3067,3174,3306,3389,3454,3548,3617,3676,3761,3824,3882,3947,4008,4069,4175,4233,4293,4352,4422,4538,4617,4697,4831,4906,4982,5079,5136,5191,5257,5327,5404,5490,5558,5634,5715,5793,5879,5966,6063,6162,6236,6306,6410,6464,6531,6621,6713,6775,6839,6902,7007,7115,7216,7325,7386,7445,7524"}}, {"source": "E:\\SDK\\caches\\transforms-3\\ae146d59d58c77d62f24ab479ef1acb9\\transformed\\appcompat-1.5.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,7529", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,7606"}}, {"source": "E:\\SDK\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "7611", "endColumns": "100", "endOffsets": "7707"}}]}]}