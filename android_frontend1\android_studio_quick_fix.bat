@echo off
chcp 65001 >nul
echo.
echo ===========================================
echo    Android Studio Quick Fix Tool
echo ===========================================
echo.

echo 🔧 Fixing XML parsing errors for Android Studio...
echo.

:: Set working directory
cd /d "%~dp0"

echo [1/6] Checking project structure...
if not exist "app\src\main\AndroidManifest.xml" (
    echo ❌ Not in Android project root directory
    echo Please run this from the android_frontend folder
    pause
    exit /b 1
)
echo ✅ Android project detected

echo.
echo [2/6] Cleaning build caches...
echo ================================
if exist ".gradle" (
    echo Removing .gradle cache...
    rmdir /s /q ".gradle" >nul 2>&1
)
if exist "app\build" (
    echo Removing app build cache...
    rmdir /s /q "app\build" >nul 2>&1
)
if exist "build" (
    echo Removing root build cache...
    rmdir /s /q "build" >nul 2>&1
)
echo ✅ Build caches cleaned

echo.
echo [3/6] Fixing XML encoding issues...
echo ==================================

:: Check and fix strings.xml encoding
if exist "app\src\main\res\values\strings.xml" (
    echo Checking strings.xml...
    findstr /B "<?xml" "app\src\main\res\values\strings.xml" >nul
    if %errorlevel% neq 0 (
        echo ⚠️  Adding XML declaration to strings.xml
        echo ^<?xml version="1.0" encoding="utf-8"?^> > temp_strings.xml
        type "app\src\main\res\values\strings.xml" >> temp_strings.xml
        move temp_strings.xml "app\src\main\res\values\strings.xml" >nul
    )
    echo ✅ strings.xml checked
)

:: Check colors.xml if exists
if exist "app\src\main\res\values\colors.xml" (
    echo Checking colors.xml...
    findstr /B "<?xml" "app\src\main\res\values\colors.xml" >nul
    if %errorlevel% neq 0 (
        echo ⚠️  Adding XML declaration to colors.xml
        echo ^<?xml version="1.0" encoding="utf-8"?^> > temp_colors.xml
        type "app\src\main\res\values\colors.xml" >> temp_colors.xml
        move temp_colors.xml "app\src\main\res\values\colors.xml" >nul
    )
    echo ✅ colors.xml checked
)

echo.
echo [4/6] Creating missing resource files...
echo =======================================

:: Create colors.xml if missing
if not exist "app\src\main\res\values\colors.xml" (
    echo Creating colors.xml...
    (
    echo ^<?xml version="1.0" encoding="utf-8"?^>
    echo ^<resources^>
    echo     ^<color name="colorPrimary"^>#2196F3^</color^>
    echo     ^<color name="colorPrimaryDark"^>#1976D2^</color^>
    echo     ^<color name="colorAccent"^>#FF4081^</color^>
    echo     ^<color name="white"^>#FFFFFF^</color^>
    echo     ^<color name="black"^>#000000^</color^>
    echo ^</resources^>
    ) > "app\src\main\res\values\colors.xml"
    echo ✅ colors.xml created
)

:: Create styles.xml if missing
if not exist "app\src\main\res\values\styles.xml" (
    echo Creating styles.xml...
    (
    echo ^<?xml version="1.0" encoding="utf-8"?^>
    echo ^<resources^>
    echo     ^<style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar"^>
    echo         ^<item name="colorPrimary"^>@color/colorPrimary^</item^>
    echo         ^<item name="colorPrimaryDark"^>@color/colorPrimaryDark^</item^>
    echo         ^<item name="colorAccent"^>@color/colorAccent^</item^>
    echo     ^</style^>
    echo ^</resources^>
    ) > "app\src\main\res\values\styles.xml"
    echo ✅ styles.xml created
)

echo.
echo [5/6] Refreshing Gradle wrapper...
echo =================================
if exist "gradlew.bat" (
    echo Gradle wrapper found
    echo Testing Gradle...
    call gradlew.bat --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ⚠️  Gradle wrapper issues detected
        echo Downloading fresh wrapper...
        call gradlew.bat wrapper --gradle-version=8.5 >nul 2>&1
    )
    echo ✅ Gradle wrapper ready
) else (
    echo ❌ Gradle wrapper not found
)

echo.
echo [6/6] Final validation...
echo ========================
echo Checking critical files:

:: Validate AndroidManifest.xml
powershell -Command "try { [xml]$xml = Get-Content 'app\src\main\AndroidManifest.xml'; echo 'OK' } catch { echo 'ERROR' }" > temp_result.txt
set /p manifest_result=<temp_result.txt
del temp_result.txt >nul 2>&1
if "%manifest_result%"=="OK" (
    echo ✅ AndroidManifest.xml is valid
) else (
    echo ❌ AndroidManifest.xml has errors
)

:: Validate strings.xml
powershell -Command "try { [xml]$xml = Get-Content 'app\src\main\res\values\strings.xml'; echo 'OK' } catch { echo 'ERROR' }" > temp_result.txt
set /p strings_result=<temp_result.txt
del temp_result.txt >nul 2>&1
if "%strings_result%"=="OK" (
    echo ✅ strings.xml is valid
) else (
    echo ❌ strings.xml has errors
)

echo.
echo ✅ Quick fix completed!
echo =====================
echo.
echo 📋 Next steps in Android Studio:
echo.
echo 1. File → Invalidate Caches and Restart
echo    (Select "Invalidate and Restart")
echo.
echo 2. After restart, File → Sync Project with Gradle Files
echo.
echo 3. Build → Clean Project
echo.
echo 4. Build → Make Project
echo.
echo 5. If still having issues:
echo    - Check Event Log (View → Tool Windows → Event Log)
echo    - Try Build → Rebuild Project
echo.
echo 🎯 Common Android Studio shortcuts:
echo    Ctrl+Shift+A : Find action
echo    Ctrl+Alt+Y   : Synchronize files
echo    Ctrl+F9      : Make project
echo.
echo =====================================
pause
