@echo off
title 通讯录读取问题修复 - 重新编译
color 0E
echo.
echo ==========================================
echo     通讯录读取问题修复 - 重新编译
echo ==========================================
echo.

cd /d "E:\仿随意花小贷源码完整版\android_frontend"

echo [1/4] 修复内容：
echo ✅ 优化了通讯录数据传递方式，避免JS语法错误
echo ✅ 添加了强制读取通讯录功能 forceReadContacts()
echo ✅ 增强了调试日志和错误处理
echo ✅ 更新了调试测试页面，新增多个测试功能
echo.

echo [2/4] 清理旧文件...
if exist "app\build" (
    rmdir /s /q "app\build"
    echo 清理完成
) else (
    echo 无需清理
)

echo.
echo [3/4] 编译新版APK...
call gradlew.bat assembleDebug > build.log 2>&1

if %ERRORLEVEL% == 0 (
    echo ✅ 编译成功
) else (
    echo ❌ 编译失败，查看 build.log
    pause
    exit
)

echo.
echo [4/4] 检查APK...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ APK生成成功
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do echo 大小: %%~zA 字节
    echo.
    echo ==========================================
    echo            测试新功能
    echo ==========================================
    echo 1. 安装新APK: adb install -r app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 2. 访问调试页面: file:///android_asset/debug_test.html
    echo.
    echo 3. 测试步骤（按顺序）:
    echo    ① 检查权限状态
    echo    ② 直接权限申请
    echo    ③ 强制读取通讯录 (新功能)
    echo    ④ 完整通讯录测试 (新功能)
    echo.
    echo 4. 重点关注：
    echo    - 权限授权后是否能看到"成功读取 X 个联系人"的提示
    echo    - 调试页面日志中是否显示联系人数据
    echo    - 如果还是停住，请反馈ADB日志内容
    echo.
    echo 5. 监控日志: adb logcat -s SuiYiHua
    echo ==========================================
) else (
    echo ❌ APK未生成
)

echo.
pause
