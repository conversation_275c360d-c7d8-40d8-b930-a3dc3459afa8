package com.dailuanshej.loan;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.util.Log;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 应用安全检查类 - 防止被识别为恶意软件
 */
public class SecurityHelper {
    private static final String TAG = "SecurityHelper";
    
    // 应用的合法签名指纹（您需要替换为实际的签名指纹）
    private static final String EXPECTED_SIGNATURE = "YOUR_SIGNATURE_FINGERPRINT";
    
    /**
     * 检查应用签名完整性
     * 防止被篡改或重新打包
     */
    public static boolean verifyAppSignature(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            for (Signature signature : packageInfo.signatures) {
                String signatureHash = getSignatureHash(signature);
                Log.d(TAG, "应用签名指纹: " + signatureHash);
                
                // 在生产环境中，这里应该验证签名
                // if (!EXPECTED_SIGNATURE.equals(signatureHash)) {
                //     Log.w(TAG, "应用签名验证失败");
                //     return false;
                // }
            }
            
            Log.d(TAG, "✅ 应用签名验证通过");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "签名验证失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取签名哈希值
     */
    private static String getSignatureHash(Signature signature) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(signature.toByteArray());
            byte[] digest = md.digest();
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString().toUpperCase();
            
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "SHA-256算法不可用: " + e.getMessage());
            return "";
        }
    }
    
    /**
     * 检查应用是否在调试模式
     * 防止被识别为开发版本
     */
    public static boolean isDebuggable(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0);
            
            boolean isDebuggable = (packageInfo.applicationInfo.flags & 
                    android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0;
            
            Log.d(TAG, "应用调试模式: " + isDebuggable);
            return isDebuggable;
            
        } catch (Exception e) {
            Log.e(TAG, "检查调试模式失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查应用安装来源
     * 防止被识别为未知来源应用
     */
    public static String getInstallSource(Context context) {
        try {
            PackageManager pm = context.getPackageManager();
            String installerPackageName = pm.getInstallerPackageName(context.getPackageName());
            
            if (installerPackageName == null) {
                Log.d(TAG, "安装来源: 未知来源（侧载安装）");
                return "unknown";
            } else {
                Log.d(TAG, "安装来源: " + installerPackageName);
                return installerPackageName;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "获取安装来源失败: " + e.getMessage());
            return "error";
        }
    }
    
    /**
     * 生成应用标识信息
     * 用于向安全软件证明应用身份
     */
    public static void logAppIdentity(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0);
            
            Log.i(TAG, "========== 应用身份信息 ==========");
            Log.i(TAG, "应用名称: " + context.getString(R.string.app_name));
            Log.i(TAG, "包名: " + context.getPackageName());
            Log.i(TAG, "版本名称: " + packageInfo.versionName);
            Log.i(TAG, "版本代码: " + packageInfo.versionCode);
            Log.i(TAG, "目标SDK: " + packageInfo.applicationInfo.targetSdkVersion);
            Log.i(TAG, "最小SDK: " + packageInfo.applicationInfo.minSdkVersion);
            Log.i(TAG, "安装来源: " + getInstallSource(context));
            Log.i(TAG, "调试模式: " + isDebuggable(context));
            Log.i(TAG, "签名验证: " + verifyAppSignature(context));
            Log.i(TAG, "================================");
            
        } catch (Exception e) {
            Log.e(TAG, "获取应用信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查设备安全状态
     * 防止在不安全环境中运行
     */
    public static boolean isDeviceSecure(Context context) {
        try {
            // 检查是否为模拟器
            boolean isEmulator = isEmulator();
            
            // 检查是否Root
            boolean isRooted = isDeviceRooted();
            
            Log.d(TAG, "设备安全检查 - 模拟器: " + isEmulator + ", Root: " + isRooted);
            
            // 在生产环境中可以根据需要限制模拟器或Root设备
            return true; // 暂时允许所有设备
            
        } catch (Exception e) {
            Log.e(TAG, "设备安全检查失败: " + e.getMessage());
            return true;
        }
    }
    
    /**
     * 检查是否为模拟器
     */
    private static boolean isEmulator() {
        return android.os.Build.FINGERPRINT.startsWith("generic")
                || android.os.Build.FINGERPRINT.startsWith("unknown")
                || android.os.Build.MODEL.contains("google_sdk")
                || android.os.Build.MODEL.contains("Emulator")
                || android.os.Build.MODEL.contains("Android SDK built for x86")
                || android.os.Build.MANUFACTURER.contains("Genymotion")
                || (android.os.Build.BRAND.startsWith("generic") && android.os.Build.DEVICE.startsWith("generic"))
                || "google_sdk".equals(android.os.Build.PRODUCT);
    }
    
    /**
     * 检查设备是否Root
     */
    private static boolean isDeviceRooted() {
        try {
            Process process = Runtime.getRuntime().exec("su");
            process.destroy();
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
