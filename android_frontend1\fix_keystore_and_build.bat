@echo off
chcp 65001 >nul
echo 🔧 修复密钥库问题并编译APK
echo ================================

echo.
echo 📋 步骤1: 清理可能的密钥库缓存
echo --------------------------------
if exist "%USERPROFILE%\.android\debug.keystore" (
    echo 发现debug密钥库: %USERPROFILE%\.android\debug.keystore
    echo 备份现有密钥库...
    copy "%USERPROFILE%\.android\debug.keystore" "%USERPROFILE%\.android\debug.keystore.backup" >nul 2>&1
    echo 删除可能损坏的密钥库...
    del "%USERPROFILE%\.android\debug.keystore" >nul 2>&1
    echo ✅ 密钥库已清理
) else (
    echo ℹ️ 未发现现有debug密钥库
)

echo.
echo 📋 步骤2: 清理项目缓存
echo --------------------------------
echo 清理Gradle缓存...
if exist ".gradle" (
    rmdir /s /q ".gradle" >nul 2>&1
    echo ✅ Gradle缓存已清理
)

if exist "app\build" (
    rmdir /s /q "app\build" >nul 2>&1
    echo ✅ 构建缓存已清理
)

echo.
echo 📋 步骤3: 检查Java环境
echo --------------------------------
java -version 2>&1 | findstr "version" >nul
if %errorlevel% equ 0 (
    echo ✅ Java环境正常
    java -version
) else (
    echo ❌ Java环境异常，请检查JAVA_HOME设置
    pause
    exit /b 1
)

echo.
echo 📋 步骤4: 开始编译Debug版本APK
echo --------------------------------
echo 使用gradlew编译debug版本（无需签名）...

gradlew.bat clean
if %errorlevel% neq 0 (
    echo ❌ Clean失败
    pause
    exit /b 1
)

echo ✅ Clean完成

gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 编译失败，尝试其他方法...
    echo.
    echo 📋 尝试方法2: 跳过签名验证
    echo --------------------------------
    gradlew.bat assembleDebug -Pandroid.injected.signing.store.file="" -Pandroid.injected.signing.store.password="" -Pandroid.injected.signing.key.alias="" -Pandroid.injected.signing.key.password=""
    
    if %errorlevel% neq 0 (
        echo ❌ 编译仍然失败
        echo.
        echo 🔍 可能的解决方案:
        echo 1. 检查是否有防病毒软件干扰
        echo 2. 尝试以管理员身份运行
        echo 3. 检查磁盘空间是否充足
        echo 4. 重启计算机后再试
        pause
        exit /b 1
    )
)

echo.
echo 📋 步骤5: 检查编译结果
echo --------------------------------
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ APK编译成功！
    echo 📱 APK位置: app\build\outputs\apk\debug\app-debug.apk
    
    echo.
    echo 📊 APK信息:
    for %%i in ("app\build\outputs\apk\debug\app-debug.apk") do (
        echo 文件大小: %%~zi 字节
        echo 修改时间: %%~ti
    )
    
    echo.
    echo 🎉 编译完成！今日更新的web前端合同功能已集成
    echo 📱 移动端合同页面文字已改为横排显示
    echo 🔒 SDK版本和联系人功能保持不变
    
) else (
    echo ❌ APK文件未找到，编译可能失败
    echo 请检查上面的错误信息
)

echo.
echo 📋 完成时间: %date% %time%
echo ================================
pause
